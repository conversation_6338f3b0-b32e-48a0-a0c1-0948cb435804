// cc.js

import { AgentFactory } from './AgentFactory.js';
import { TeamFactory } from './TeamFactory.js';
import { AgencyFactory } from './AgencyFactory.js';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs/promises';
import dotenv from 'dotenv';
import { GeminiImageGenerator } from './ig.js'; // Assuming ig.js exports this class

// Load environment variables from .env file
dotenv.config();

async function main() {
  try {
    const GEMINI_API_KEY = process.env.GEMINI_API_KEY;

    if (!GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY environment variable is not set');
    }

    // Create agent factory with API keys and register the tool
    const agentFactory = new AgentFactory({
      defaultProvider: 'gemini',
      apiKeys: {
        gemini: GEMINI_API_KEY
      }
    });

    // Register image generation tool using the same pattern as vacation planner
    const geminiImageGeneratorInstance = new GeminiImageGenerator();

    // Simple wrapper function that handles the image generation
    const imageGenerationFunction = async (params) => {
        console.log('🎨 Generating image with params:', params);

        // Handle different parameter formats - expect query property like other tools
        const prompt = typeof params === 'string' ? params : params.query || params.prompt;

        if (!prompt || typeof prompt !== 'string' || prompt.trim().length < 10) {
            throw new Error('Image prompt must be a non-empty string and at least 10 characters long.');
        }

        const result = await geminiImageGeneratorInstance.generateImage(prompt);
        console.log('✅ Image generated successfully:', result.imagePath);
        return result;
    };

    // Register the tool using the same pattern as vacation planner
    const imageGeneratorTool = agentFactory.registerTool('generateImage', imageGenerationFunction, {
      description: 'Generates an image based on a detailed text prompt. Returns a path or URL to the generated image.',
      isAsync: true,
      timeout: 30000
    });

    // Add monitoring
    imageGeneratorTool.addMonitor((toolName, params, duration, success, result, error) => {
      console.log(`Tool ${toolName} executed in ${duration}ms, success: ${success}`);
      if (success && result && result.imagePath) {
        console.log(`Generated image available at: ${result.imagePath}`);
      }
      if (error) {
        console.log(`Error during image generation: ${error.message}`);
      }
    });

    // Create team factory
    const teamFactory = new TeamFactory({ agentFactory });

    // Create agency factory
    const agencyFactory = new AgencyFactory({
      teamFactory,
      agentFactory
    });

    // Get the directory name
    const __dirname = path.dirname(fileURLToPath(import.meta.url));

    // Path to the config file
    const configPath = path.join(__dirname, 'ct.json');

    // Load content creation agency configuration
    const agency = await agencyFactory.loadAgencyFromFile(configPath);

    console.log('Agency loaded successfully:');
    console.log('- name:', agency.name);
    console.log('- team:', Object.keys(agency.team));
    console.log('- brief:', Object.keys(agency.brief));

    console.log('Executing brief: blog-post-007');

    // Extract inputs from the brief for the blog post
    const brief = agency.brief['blog-post-007'];

    // Assign the job to the content creation team
    console.log('Assigning job "blog-post-007" to "contentCreationTeam" (type: team)...');
    agency.assignJob('blog-post-007', 'contentCreationTeam', 'team');

    // Execute the content creation workflow with the initial brief inputs
    console.log('Executing job...');
    console.log('Using initial inputs for the workflow:');
    console.log('- topic:', brief.topic);
    console.log('- brief:', brief.overview);

    const results = await agency.execute('blog-post-007', {
      topic: brief.topic,
      brief: brief.overview
    });

    console.log('Job execution completed. Results received.');

    console.log('\n=== CONTENT CREATION RESULTS ===\n');

    console.log('Results object contains keys:', Object.keys(results));

    // Display and save results
    let markdownContent = '# CONTENT CREATION RESULTS\n\n';
    markdownContent += `Results object contains keys: [ ${Object.keys(results).join(', ')} ]\n\n`;

    const team = agency.team['contentCreationTeam'];
    for (const jobName of team.workflow) {
        if (results[jobName]) {
            const resultText = typeof results[jobName] === 'object' ? JSON.stringify(results[jobName], null, 2) : results[jobName];
            console.log(`${jobName.replace(/([A-Z])/g, ' $1').trim()} (length: ${resultText.length} characters):`);
            console.log(resultText);
            console.log('\n');
            markdownContent += `## ${jobName.replace(/([A-Z])/g, ' $1').trim()} (length: ${resultText.length} characters):\\n${resultText}\\n\\n`;
        } else {
            console.log(`WARNING: No ${jobName} results found!`);
            markdownContent += `## ${jobName.replace(/([A-Z])/g, ' $1').trim()}:\\nWARNING: No results found for this step.\\n\\n`;
        }
    }

    // Special handling for the image (if it's a path or URL)
    if (results.generateImage && results.generateImage.imagePath) {
        markdownContent += `## Generated Image:\\n![Generated Image](${results.generateImage.imagePath})\\n\\n`;
    } else if (results.generateImage) {
        markdownContent += `## Generated Image (Text Description):\\n${JSON.stringify(results.generateImage, null, 2)}\\n\\n`;
    }

    const resultsFilePath = path.join(__dirname, 'content-results.md');
    await fs.writeFile(resultsFilePath, markdownContent, 'utf8');
    console.log(`Results saved to ${resultsFilePath}`);

    console.log('Content creation workflow completed successfully!');

  } catch (error) {
    console.error('Error during workflow execution:', error);
    if (error.response && error.response.data) {
      console.error('API Error Details:', error.response.data);
    }
  }
}

main();