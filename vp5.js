// vp5.js - Orchestrator for the vacation planning workflow defined in vp5.json

import { AgentFactory } from './AgentFactory.js';
import { TeamFactory } from './TeamFactory.js';
import { AgencyFactory } from './AgencyFactory.js';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import fs from 'fs/promises';
// Import tools used in vp5.json
import { webSearch } from './search_tool.js'; // Assuming search_tool.js exists and exports webSearch
// Note: datetime and calculator are not used in the simplified vp5.json, so they are commented out.
// import { dateTimeTool } from './datetime.js'; // Assuming datetime.js exists and exports dateTimeTool
// import { calculatorTool as calculator } from './calculator.js'; // Assuming calculator.js exists and exports calculatorTool
import { kayakFlightSearch } from './kayakFlightSearchTool.js'; // Import the newly created tool

// Load environment variables from a .env file.
dotenv.config();

async function main() {
  try {
    const GEMINI_API_KEY = process.env.GEMINI_API_KEY;

    if (!GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY environment variable is not set');
    }

    // Initialize the factories with the Gemini API key.
    const agentFactory = new AgentFactory({
      defaultProvider: 'gemini',
      apiKeys: {
        gemini: GEMINI_API_KEY
      }
    });

    // Register tools used by the agents in vp5.json
    const travelSearchTool = agentFactory.registerTool('travelSearch', webSearch, {
      description: 'Search for travel-related information including destinations, accommodations, and activities',
      isAsync: true,
      timeout: 15000
    });

    // Register the newly created kayakFlightSearch tool
    const kayakFlightSearchToolInstance = agentFactory.registerTool('kayakFlightSearch', kayakFlightSearch, {
      description: 'Searches for the cheapest flight details on Kayak.com',
      isAsync: true,
      timeout: 30000 // Increased timeout for browser operations
    });

    // Add validators and monitors (optional, but good practice)
    travelSearchTool.addValidator((params) => {
      if (!params.query || params.query.length < 3) {
        throw new Error('Search query must be at least 3 characters long');
      }
    });

    travelSearchTool.addMonitor((toolName, params, duration, success, result, error) => {
      console.log(`Tool ${toolName} executed in ${duration}ms, success: ${success}`);
      if (error) console.log(`Error: ${error.message}`);
    });

    kayakFlightSearchToolInstance.addValidator((params) => {
      if (!params.departureLocation || !params.destinationLocation || !params.departureDate || !params.returnDate) {
        throw new Error('kayakFlightSearch requires departureLocation, destinationLocation, departureDate, and returnDate');
      }
    });

    kayakFlightSearchToolInstance.addMonitor((toolName, params, duration, success, result, error) => {
      console.log(`Tool ${toolName} executed in ${duration}ms, success: ${success}`);
      if (error) console.log(`Error: ${error.message}`);
    });


    // Create the team and agency factories, passing the agentFactory instance.
    const teamFactory = new TeamFactory({ agentFactory });
    const agencyFactory = new AgencyFactory({
      teamFactory,
      agentFactory
    });

    // Get the directory name of the current module.
    const __dirname = path.dirname(fileURLToPath(import.meta.url));

    // Define the path to the vp5 configuration file.
    const configPath = path.join(__dirname, 'vp5.json');

    // Load the agency configuration from the JSON file.
    const agency = await agencyFactory.loadAgencyFromFile(configPath);

    console.log('Vacation Planner Agency (vp5.json) loaded successfully:');
    console.log('- name:', agency.name);
    console.log('- team:', Object.keys(agency.team));
    console.log('- brief:', Object.keys(agency.brief));

    // Assuming the job to execute is 'beach-vacation-001' as in vp5.json
    const jobId = 'beach-vacation-001';
    const teamName = 'vacationTeam'; // Assuming the team name is also 'vacationTeam'

    if (!agency.brief[jobId]) {
        throw new Error(`Brief with ID "${jobId}" not found in vp5.json`);
    }
    if (!agency.team[teamName]) {
        throw new Error(`Team with name "${teamName}" not found in vp5.json`);
    }

    const brief = agency.brief[jobId];

    // Assign the job to the team
    console.log(`Assigning job "${jobId}" to "${teamName}" (type: team)...`);
    agency.assignJob(jobId, teamName, 'team');

    // Execute the workflow
    console.log('Executing job...');
    console.log('Using initial inputs for the workflow:');
    console.log('- brief:', JSON.stringify(brief, null, 2));

    const results = await agency.execute(jobId, brief);

    console.log('Job execution completed. Results received.');

    console.log('\n=== VACATION PLANNING RESULTS ===\n');

    console.log('Results object contains keys:', Object.keys(results));

    // Display the results for each step in the workflow.
    let markdownContent = '# VACATION PLANNING RESULTS\n\n';
    markdownContent += `Results object contains keys: [ ${Object.keys(results).join(', ')} ]\n\n`;

    const team = agency.team[teamName];
    for (const jobName of team.workflow) {
      if (results[jobName]) {
        const resultText = typeof results[jobName] === 'object' ? JSON.stringify(results[jobName], null, 2) : results[jobName];
        console.log(`${jobName.replace(/([A-Z])/g, ' $1').trim()} (length: ${resultText.length} characters):`);
        console.log(resultText);
        console.log('\n');
        markdownContent += `## ${jobName.replace(/([A-Z])/g, ' $1').trim()} (length: ${resultText.length} characters):\n${resultText}\n\n`;
      } else {
        console.log(`WARNING: No ${jobName} results found!`);
        markdownContent += `## ${jobName.replace(/([A-Z])/g, ' $1').trim()}:\nWARNING: No results found for this step.\n\n`;
      }
    }

    // Save the results to a Markdown file.
    const resultsFilePath = path.join(__dirname, 'vacation-results3.md'); // Using a new file name
    await fs.writeFile(resultsFilePath, markdownContent, 'utf8');
    console.log(`Results saved to ${resultsFilePath}`);

    console.log('Vacation planning workflow completed successfully!');

  } catch (error) {
    console.error('Error during workflow execution:', error);
    if (error.response && error.response.data) {
      console.error('API Error Details:', error.response.data);
    }
  }
}

// Execute the main function.
main();
