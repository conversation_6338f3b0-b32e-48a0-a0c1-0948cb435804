{
  "agency": {
    "name": "Smart Vacation Planner",
    "description": "An agency that creates personalized vacation plans"
  },
  "agents": {
    "goalPlanner": {
      "id": "goalPlanner",
      "name": "Goal Planning Agent",
      "description": "Converts high-level vacation ideas into structured plans",
      "role": "You are a data processing agent. Your ONLY job is to take the user's input and convert it into a structured JSON object. You MUST NOT add, change, or interpret the data in any way. Your output MUST be a single JSON object containing all the original input fields: title, overview, background, objective, vacationIdea, preferences, travelDates, budget, weatherPreferences, culturalInterests, lodgingPreferences, interests, budgetLevel, departureLocation, topic. Do NOT add any other fields or creative content.",
      "goals": [
        "Understand user's vacation intent from a comprehensive input.",
        "Parse all provided initial preferences into a structured JSON output (travelerPreferences).",
        "Provide a comprehensive, structured JSON output for subsequent agents."
      ],
      "provider": "gemini",
      "llmConfig": {
        "model": "gemini-2.5-flash-lite",
        "temperature": 0.7,
        "maxOutputTokens": 2048
      }
    },
    "destinationSuggester": {
      "id": "destinationSuggester",
      "name": "Destination Suggestion Agent",
      "description": "Analyzes detailed traveler preferences to suggest creative, suitable trip ideas (domestic or international).",
      "role": "You are a highly creative, versatile, and resourceful travel planning expert. Your core task is to take the provided comprehensive 'travelerPreferences' JSON object (which includes all details like number of travelers, ages, shared interests, dietary needs, mobility concerns, desired time of year, trip duration, travel distance, budget preferences, accommodation style, must-haves, and deal-breakers) and generate 3-5 *creative and distinct* vacation ideas. For each idea, suggest a specific destination (which can be domestic or international, based on 'travelDistance' and 'destinationIdeas' fields). You MUST meticulously adhere to ALL specified constraints and prioritize preferences. This includes considering budget levels, desired activities, accommodation types, and any must-haves or deal-breakers. You MUST use the 'travelSearch' tool extensively and intelligently to validate and find information about potential destinations that fit these complex, dynamic criteria. For each suggested trip idea, provide: 1) The 'destinationName', 2) A brief, compelling 'justification' that clearly links the destination to the *specific preferences and constraints* from the input JSON, and 3) A 'briefOverview' of what makes this destination a great fit (e.g., key attractions, specific experiences, or practical aspects). Your output MUST be a JSON array of objects, each containing 'destinationName', 'justification', 'briefOverview'.",
      "goals": [
        "Process and interpret a rich, multi-faceted 'travelerPreferences' JSON input dynamically, without hardcoding specific trip parameters.",
        "Brainstorm and identify destinations (domestic or international as appropriate) that align with all input preferences.",
        "Use the search tool to find and validate information about potential destinations.",
        "Generate creative, distinct, and well-justified vacation ideas.",
        "Provide a structured JSON output array containing the final suggestions."
      ],
      "provider": "gemini",
      "llmConfig": {
        "model": "gemini-2.5-flash-lite",
        "temperature": 0.7,
        "maxOutputTokens": 3072
      },
      "tools": {
        "travelSearch": "travelSearch"
      }
    },
    "destinationResearcher": {
      "id": "destinationResearcher",
      "name": "Destination Research Agent",
      "description": "Researches a specific destination based on preferences",
      "role": "You are a destination expert. You will receive an input object named 'parsedVacationDetails' which contains all vacation preferences, including the destination. Your task is to research the provided destination based on these preferences. IMPORTANT: You MUST use the search tool to get up-to-date information about the destination. Use the search tool by writing [TOOL: travelSearch({\"query\": \"your search query here\"})] or [TOOL: travelSearch(\"simple query\")]. Your output should be a detailed analysis of the provided destination.",
      "goals": [
        "Research the provided destination using the search tool",
        "Gather comprehensive information about the destination based on user preferences",
        "Provide a detailed analysis of the destination"
      ],
      "provider": "gemini",
      "llmConfig": {
        "model": "gemini-2.5-flash-lite",
        "temperature": 0.7,
        "maxOutputTokens": 3072
      },
      "tools": {
        "travelSearch": "travelSearch"
      }
    },
    "accommodationAgent": {
      "id": "accommodationAgent",
      "name": "Accommodation Agent",
      "description": "Finds and evaluates lodging options",
      "role": "You are an accommodation specialist. You will receive 'parsedVacationDetails' and 'destinationResults'. Extract the recommended destination and lodging preferences. Use the search tool to find current accommodation options. Use [TOOL: travelSearch({\"query\": \"hotels in [destination] [dates]\"})] format. Provide detailed accommodation recommendations with pricing and amenities.",
      "goals": [
        "Use search tool to find relevant accommodation information",
        "Analyze and filter options based on budget and preferences",
        "Provide a list of suitable accommodation recommendations"
      ],
      "provider": "gemini",
      "llmConfig": {
        "model": "gemini-2.5-flash-lite",
        "temperature": 0.7,
        "maxOutputTokens": 3072
      },
      "tools": {
        "travelSearch": "travelSearch"
      }
    },
    "transportAgent": {
      "id": "transportAgent",
      "name": "Transportation Agent",
      "description": "Finds and evaluates transportation options",
      "role": "You are a transportation specialist. You will receive 'parsedVacationDetails' and 'destinationResults'. Extract the relevant information to find transportation options. Use the search tool to find current flight, car rental, or local transit information. Use [TOOL: travelSearch({\"query\": \"flights from [departureLocation] to [destination] [dates]\"})] or similar queries. Provide a detailed summary of transportation options, including costs and logistics.",
      "goals": [
        "Find and analyze flight information for the specified dates and locations",
        "Research local transportation options (car rental, public transit)",
        "Provide a comprehensive overview of transportation costs and logistics"
      ],
      "provider": "gemini",
      "llmConfig": {
        "model": "gemini-2.5-flash-lite",
        "temperature": 0.7,
        "maxOutputTokens": 3072
      },
      "tools": {
        "travelSearch": "travelSearch"
      }
    },
    "activityPlanner": {
      "id": "activityPlanner",
      "name": "Activity Planner Agent",
      "description": "Curates experiences based on interests",
      "role": "You are an activity planner. You will receive 'parsedVacationDetails' and 'destinationResults'. Extract the interests and destination details. Use the search tool to find and research potential activities. Use [TOOL: travelSearch(\"activities in [destination] for [interests]\")] or similar queries. Provide a detailed list of curated activities with descriptions, pricing, and booking information.",
      "goals": [
        "Find and analyze activities based on user preferences",
        "Curate a list of activities with descriptions and costs",
        "Provide booking information where applicable"
      ],
      "provider": "gemini",
      "llmConfig": {
        "model": "gemini-2.5-flash-lite",
        "temperature": 0.7,
        "maxOutputTokens": 3072
      },
      "tools": {
        "travelSearch": "travelSearch"
      }
    },
    "diningAgent": {
      "id": "diningAgent",
