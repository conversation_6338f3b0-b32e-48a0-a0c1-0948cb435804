The `Agent.js` file defines a JavaScript framework for building AI agents, incorporating an event system, memory management, and flexible tool handling.

Here's a breakdown of the key classes and their functionalities:

**1. `EventEmitter` Class:**
   - Provides a basic event system.
   - `on(eventName, listener)`: Subscribes a listener function to an event. Returns an unsubscribe function.
   - `off(eventN<PERSON>, listenerToRemove)`: Unsubscribes a listener from an event.
   - `emit(eventName, data)`: Emits an event, calling all registered listeners with the provided data. Includes error handling for listeners.
   - `once(eventName, listener)`: Subscribes a listener to an event for a single occurrence, automatically unsubscribing after the first emission.

**2. `MemoryManager` Class:**
   - Manages the agent's memory, including conversation history and a key-value store.
   - `constructor(config)`: Initializes with an optional `maxHistoryLength` (default 100) and an `EventEmitter` instance.
   - `addToHistory(entry)`: Adds an entry (with a timestamp) to the conversation history. Trims the history if it exceeds `maxHistoryLength`. Emits `historyUpdated` event.
   - `getHistory(limit)`: Retrieves a portion of the conversation history.
   - `remember(key, value)`: Stores a key-value pair. Emits `memoryUpdated` event.
   - `recall(key)`: Retrieves a value by its key.
   - `clear()`: Clears both conversation history and the key-value store. Emits `memoryCleared` event.
   - `on(eventName, listener)`: Allows subscribing to memory-specific events (e.g., `historyUpdated`, `memoryUpdated`, `memoryCleared`).

**3. `LLMProvider` Class (Interface):**
   - Defines a contract for interacting with Large Language Model (LLM) providers.
   - `generateContent(options)`: Abstract method that subclasses must implement to generate content using an LLM.

**4. `ToolHandler` Class (Interface):**
   - Defines a contract for handling tool calls within the LLM's response.
   - `handleToolCalls(responseText, tools, callToolFn, formatToolResultFn)`: Abstract method that subclasses must implement to parse tool calls from text, execute them, and format their results back into the response.

**5. `DefaultToolHandler` Class:**
   - A concrete implementation of `ToolHandler`.
   - Handles tool calls using a specific syntax: `[TOOL: toolName(params)]`.
   - Parses the tool name and parameters, executes the corresponding tool function, and replaces the tool call syntax with the formatted tool result.

**6. `AdvancedToolHandler` Class:**
   - A more sophisticated implementation of `ToolHandler`.
   - `constructor(config)`: Allows configuration for `retryAttempts` (default 3), `retryDelay` (default 1000ms), and `fallbackEnabled` (default true).
   - `handleToolCalls(...)`:
     - Extracts tool calls using two formats:
       - `[TOOL: toolName(params)]`
       - JSON function calling: `{"tool": "toolName", "parameters": { ... }}`
     - Includes retry logic (`_executeWithRetry`) for tool execution failures.
     - Supports a fallback mechanism if a tool fails or is unavailable.
   - `_extractToolCalls(responseText)`: Internal method to find and parse tool calls based on the defined formats.
   - `_parseParams(paramString)`: Internal method to parse parameter strings, attempting JSON parsing first and then falling back to a simple query format.
   - `_executeWithRetry(tool, params, callToolFn, attempt)`: Internal method to execute a tool with retries.

**7. `Agent` Class:**
   - The core class for creating an AI agent.
   - `constructor(config)`:
     - Requires `id`, `name`, `description`, `role`, and `llmProvider` in the configuration.
     - Initializes core properties, LLM configuration, and integrates the `AdvancedToolHandler` (by default), `MemoryManager`, and `EventEmitter`.
     - Sets up default `inputFormatter`, `responseProcessor`, and `toolResultFormatter` functions.
     - Calls `_refreshToolSchemas()` to initialize tool schemas.
   - `getStatus()`: Returns the agent's current status.
   - `setStatus(newStatus)`: Sets the agent's status and emits a `statusChanged` event.
   - `addTool(name, tool)` (overloaded): Adds a tool to the agent.
     - The later `addTool` method (bottom of the file) also refreshes tool schemas and emits a `toolAdded` event with schema information.
   - `getTools()`: Returns all registered tools.
   - `on(eventName, listener)`: Subscribes to agent-specific events (e.g., `runStarted`, `llmResponseReceived`, `toolCalled`, `runCompleted`).
   - `run(input, context)`:
     - The main method to process an input using the agent.
     - Sets status to 'working' and emits `runStarted`.
     - Formats input, calls the LLM provider to generate content.
     - Processes the LLM response.
     - If tools are available and a `toolHandler` is configured, it handles tool calls within the response.
     - Stores the interaction in memory.
     - Sets status to 'idle' and emits `runCompleted`.
     - Returns either the raw tool result (if a tool was called) or the processed LLM response.
     - Includes comprehensive event emissions at various stages of the run.
   - `llmProviderSpecificConfig()`: An optional method for subclasses to provide LLM-specific configuration.
   - `setPersona(persona)`: Modifies the agent's `role` based on predefined persona templates (strategist, analyst, creative), influencing its reasoning style.
   - `removeTool(toolName)`: Removes a tool and refreshes tool schemas. Emits `toolRemoved` event.
   - `_refreshToolSchemas()`: Internal method to update the `toolSchemas` array, used by LLM providers that support tool schema updates.
   - `getTool(toolName)`: Retrieves a specific tool by name.
   - `listTools()`: Returns an array of tool names.

**Overall Architecture:**

The framework promotes a modular and extensible design:
- **Event-Driven:** The `EventEmitter` allows for loose coupling between components and provides hooks for monitoring agent activity.
- **Pluggable Components:** `LLMProvider` and `ToolHandler` are interfaces, enabling developers to integrate different LLMs and implement custom tool handling logic.
- **Memory Management:** The `MemoryManager` centralizes conversation history and key-value storage.
- **Tool Integration:** Agents can incorporate tools, allowing them to interact with external systems or perform specific functions. The `AdvancedToolHandler` supports flexible tool call parsing and robust execution.
- **Configurable Agent Behavior:** The `Agent` class allows for extensive configuration, including LLM parameters, memory settings, and custom formatters.
- **Persona System:** The `setPersona` method offers a simple way to influence the agent's reasoning style by appending specific instructions to its role.