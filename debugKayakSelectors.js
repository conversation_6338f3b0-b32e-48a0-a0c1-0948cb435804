// debugKayakSelectors.js
import { chromium } from 'playwright';

async function debugKayakSelectors() {
  let browser;
  try {
    console.log('Launching browser...');
    browser = await chromium.launch({ headless: false });
    const context = await browser.newContext();
    const page = await context.newPage();
    
    console.log('Navigating to Kayak.com...');
    await page.goto('https://www.kayak.com/flights', { waitUntil: 'networkidle', timeout: 60000 });
    
    console.log('Waiting for page to load...');
    await page.waitForTimeout(5000);
    
    // Check if we're on the right page
    const title = await page.title();
    console.log('Page title:', title);
    console.log('Current URL:', page.url());
    
    // Try to find ALL input elements first
    console.log('\n--- Finding All Input Elements ---');
    const inputs = await page.$$('input');
    console.log(`Found ${inputs.length} input elements`);
    
    for (let i = 0; i < Math.min(inputs.length, 10); i++) {
      const input = inputs[i];
      const placeholder = await input.getAttribute('placeholder') || 'none';
      const name = await input.getAttribute('name') || 'none';
      const id = await input.getAttribute('id') || 'none';
      const ariaLabel = await input.getAttribute('aria-label') || 'none';
      const type = await input.getAttribute('type') || 'none';
      const className = await input.getAttribute('class') || 'none';
      
      console.log(`Input ${i + 1}:`);
      console.log(`  id: "${id}"`);
      console.log(`  name: "${name}"`);
      console.log(`  placeholder: "${placeholder}"`);
      console.log(`  aria-label: "${ariaLabel}"`);
      console.log(`  type: "${type}"`);
      console.log(`  class: "${className}"`);
      console.log('---');
    }
    
    // Try specific selectors that are commonly used on Kayak
    console.log('\n--- Testing Specific Selectors ---');
    
    const selectorsToTest = [
      // Origin/Departure selectors
      'input[aria-label*="Flying from"]',
      '[name="origin"]',
      '#origin-input',
      '[data-testid="origin-input"]',
      '.origin-input',
      
      // Destination selectors
      'input[aria-label*="Flying to"]',
      '[name="destination"]',
      '#destination-input',
      '[data-testid="destination-input"]',
      
      // Date selectors
      'input[aria-label*="Depart"]',
      '[name="depart"]',
      '#depart-input',
      '[data-testid="depart-input"]',
      
      'input[aria-label*="Return"]',
      '[name="return"]',
      '#return-input',
      '[data-testid="return-input"]',
      
      // Search button selectors
      'button[type="submit"]',
      '.search-button',
      '#search-button',
      '[data-testid="search-button"]',
      'button:has-text("Search")'
    ];
    
    for (const selector of selectorsToTest) {
      try {
        const elements = await page.$$(selector);
        if (elements.length > 0) {
          console.log(`✓ Found ${elements.length} element(s) with selector: ${selector}`);
          for (let i = 0; i < Math.min(elements.length, 3); i++) {
            const element = elements[i];
            const isVisible = await element.isVisible();
            const isEditable = await element.isEditable();
            console.log(`  Element ${i + 1}: visible=${isVisible}, editable=${isEditable}`);
          }
        } else {
          console.log(`✗ No elements found with selector: ${selector}`);
        }
      } catch (error) {
        console.log(`✗ Error with ${selector}: ${error.message}`);
      }
    }
    
    console.log('\nDebug complete. Browser will remain open for 30 seconds for manual inspection...');
    await page.waitForTimeout(30000);
    
  } catch (error) {
    console.error('Error in debugKayakSelectors:', error);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Make sure the function runs
debugKayakSelectors().then(() => {
  console.log('Debug script completed');
}).catch(console.error);