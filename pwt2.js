// playwrightTool.js
import { chromium } from 'playwright';

class PlaywrightTool {
    constructor() {
        this.browser = null;
        this.page = null;
    }

    async initialize(options = {}) {
        try {
            this.browser = await chromium.launch(options);
            this.page = await this.browser.newPage();
        } catch (error) {
            console.error("Error initializing Playwright:", error);
            throw error;
        }
    }

    async navigateTo(url) {
        if (!this.page) {
            await this.initialize();
        }
        try {
            await this.page.goto(url);
        } catch (error) {
            console.error(`Error navigating to ${url}:`, error);
            throw error;
        }
    }

    /**
     * Fills a form field.
     * @param {string} selector - The selector for the input element.
     * @param {string} data - The data to fill.
     * @param {Object} [options] - Optional parameters.
     * @param {boolean} [options.handleAutocomplete=false] - If true, waits after filling and optionally presses Enter.
     * @param {number} [options.waitAfterFill=1000] - Milliseconds to wait after filling if handleAutocomplete is true.
     * @param {boolean} [options.pressEnter=false] - If true, presses Enter after waiting when handleAutocomplete is true.
     * @param {boolean} [options.clearBeforeFill=true] - If true, clears the input field before filling.
     */
    async fillForm(selector, data, options = {}) {
        if (!this.page) {
            await this.initialize();
        }
        try {
            if (options.clearBeforeFill !== false) {
                await this.page.fill(selector, '');
                await this.page.waitForTimeout(100);
            }

            await this.page.fill(selector, data);

            if (options.handleAutocomplete) {
                const waitTime = options.waitAfterFill ?? 1000;
                await this.page.waitForTimeout(waitTime);
                if (options.pressEnter) {
                    await this.page.press(selector, 'Enter');
                }
            }
        } catch (error) {
            console.error(`Error filling form with selector "${selector}":`, error);
            throw error;
        }
    }

    /**
     * Selects a date from a date picker widget.
     * @param {string} datePickerTriggerSelector - Selector for the element that opens the date picker.
     * @param {string} dateToSelect - The full date string (e.g., "Month Day, Year") that can be used to identify the date element's aria-label.
     */
    async selectDate(datePickerTriggerSelector, dateToSelect, nextMonthButtonSelector = '[aria-label="Next month"]', prevMonthButtonSelector = '[aria-label="Previous month"]') {
        if (!this.page) {
            await this.initialize();
        }
        try {
            console.log(`Clicking date picker trigger: ${datePickerTriggerSelector}`);
            await this.clickElement(datePickerTriggerSelector);

            const monthHeaderOrDateCellSelector = 'caption.w0lb-month-name, .g_wN-month-name, [aria-label*="2025"]';
            await this.page.waitForSelector(monthHeaderOrDateCellSelector, { state: 'visible', timeout: 10000 });
            console.log('Date picker elements (month header or date cell) are visible.');
            await this.page.waitForTimeout(1000);

            await this.page.screenshot({ path: 'debug_calendar_visible.png' });
            console.log('Screenshot taken: debug_calendar_visible.png');

            const targetDate = new Date(dateToSelect);
            const targetMonth = targetDate.getMonth();
            const targetYear = targetDate.getFullYear();

            let monthFound = false;
            let attempts = 0;
            const maxAttempts = 24;

            while (!monthFound && attempts < maxAttempts) {
                const monthHeaderSelector = 'caption.w0lb-month-name, .g_wN-month-name';
                const monthHeaders = await this.page.$$eval(monthHeaderSelector, elements => elements.map(el => el.textContent.trim()));

                console.log(`Current displayed months: ${monthHeaders.join(', ')}`);

                if (monthHeaders.length > 0) {
                    for (const headerText of monthHeaders) {
                        try {
                            const parsedHeaderDate = new Date(headerText + ' 1');
                            if (parsedHeaderDate.getMonth() === targetMonth && parsedHeaderDate.getFullYear() === targetYear) {
                                monthFound = true;
                                console.log(`Target month "${headerText}" found.`);
                                break;
                            }
                        } catch (e) {
                            console.warn(`Could not parse month header "${headerText}". Error: ${e.message}`);
                        }
                    }
                }

                if (monthFound) {
                    break;
                }

                if (monthHeaders.length > 0) {
                     const firstDisplayedMonthDate = new Date(monthHeaders[0] + ' 1');
                     const firstDisplayedMonth = firstDisplayedMonthDate.getMonth();
                     const firstDisplayedYear = firstDisplayedMonthDate.getFullYear();

                     if (targetYear < firstDisplayedYear || (targetYear === firstDisplayedYear && targetMonth < firstDisplayedMonth)) {
                         console.log('Navigating to previous month...');
                         await this.clickElement(prevMonthButtonSelector);
                     } else {
                         console.log('Navigating to next month...');
                         await this.clickElement(nextMonthButtonSelector);
                     }
                } else {
                    console.warn("Still no month headers found, attempting to click next month as a fallback.");
                    await this.clickElement(nextMonthButtonSelector);
                }

                await this.page.waitForTimeout(750);
                attempts++;
            }

            if (!monthFound) {
                throw new Error(`Target month ${targetDate.toLocaleString('default', { month: 'long' })} ${targetYear} not found after ${maxAttempts} attempts.`);
            }

            // --- CRITICAL REVISION: Using getByRole and explicit hover ---
            const dateButtonLocator = this.page.getByRole('button', { name: dateToSelect, exact: true });
            console.log(`Attempting to interact with date button: ${dateToSelect}`);

            await this.page.screenshot({ path: 'debug_before_date_click_final.png' });
            console.log('Screenshot taken: debug_before_date_click_final.png');

            try {
                // Wait for the locator to be visible and enabled
                await dateButtonLocator.waitFor({ state: 'visible', timeout: 10000 });
                await dateButtonLocator.waitFor({ state: 'enabled', timeout: 5000 });

                console.log('Attempting to hover over the date button...');
                await dateButtonLocator.hover({ timeout: 5000 }); // Explicitly hover first
                await this.page.waitForTimeout(500); // Small pause after hover

                console.log('Attempting to click the date button using locator.click() with force: true...');
                await dateButtonLocator.click({ force: true, timeout: 5000 });

            } catch (clickError) {
                console.error(`Failed to click date "${dateToSelect}" after multiple attempts: ${clickError.message}`);
                // Re-take screenshot on failure to capture final state before throwing
                await this.page.screenshot({ path: 'debug_date_click_failure.png' });
                console.log('Screenshot taken: debug_date_click_failure.png');
                throw clickError; // Re-throw the ultimate failure
            }

            await this.page.waitForTimeout(1000); // Longer wait after successful click to ensure registration

            console.log(`Successfully selected date: ${dateToSelect}`);

        } catch (error) {
            console.error(`Error selecting date "${dateToSelect}" with trigger "${datePickerTriggerSelector}":`, error);
            throw error;
        }
    }


    async scrapeContent(selector) {
        if (!this.page) {
            await this.initialize();
        }
        try {
            const elements = await this.page.$$(selector);
            if (elements.length > 1) {
                const contents = [];
                for (const element of elements) {
                    contents.push(await element.textContent());
                }
                return contents;
            } else if (elements.length === 1) {
                return await elements[0].textContent();
            } else {
                return null;
            }
        } catch (error) {
            console.error(`Error scraping content with selector "${selector}":`, error);
            throw error;
        }
    }

    async scrapeAttribute(selector, attributeName) {
        if (!this.page) {
            await this.initialize();
        }
        try {
            const elements = await this.page.$$(selector);
            if (elements.length > 0) {
                const attributes = [];
                for (const element of elements) {
                    const attributeValue = await element.getAttribute(attributeName);
                    attributes.push(attributeValue);
                }
                return attributes.length === 1 ? attributes[0] : attributes;
            } else {
                return null;
            }
        } catch (error) {
            console.error(`Error scraping attribute "${attributeName}" with selector "${selector}":`, error);
            throw error;
        }
    }

    async scrapeAllText() {
        if (!this.page) {
            await this.initialize();
        }
        try {
            return await this.page.evaluate(() => document.body.innerText);
        } catch (error) {
            console.error("Error scraping all text from the page:", error);
            throw error;
        }
    }

    async clickElement(selector) {
        if (!this.page) {
            await this.initialize();
        }
        try {
            await this.page.click(selector);
        } catch (error) {
            console.error(`Error clicking element with selector "${selector}":`, error);
            throw error;
        }
    }

    async takeScreenshot(path) {
        if (!this.page) {
            await this.initialize();
        }
        try {
            await this.page.screenshot({ path: path });
        } catch (error) {
            console.error(`Error taking screenshot at path "${path}":`, error);
            throw error;
        }
    }

    async close() {
        if (this.browser) {
            try {
                await this.browser.close();
            } catch (error) {
                console.error("Error closing browser:", error);
            } finally {
                this.browser = null;
                this.page = null;
            }
        }
    }
}

export default PlaywrightTool;