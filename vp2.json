{"agency": {"name": "Simplified Vacation Planner", "description": "A simple 2-agent team that plans a vacation."}, "agents": {"goalPlanner": {"id": "goalPlanner", "name": "Goal Planning Agent", "description": "Converts high-level vacation ideas into structured plans.", "role": "You are a data processing agent. Your ONLY job is to take the user's input and convert it into a structured JSON object. You MUST NOT add, change, or interpret the data in any way. Your output MUST be a single JSON object containing all the original input fields: vacationIdea, preferences, travelDates, budget, numTravelers. Do NOT add any other fields or creative content.", "goals": ["Understand user's vacation intent.", "Parse all provided initial preferences into a structured JSON output.", "Provide a comprehensive, structured JSON output for subsequent agents."], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.7, "maxOutputTokens": 2048}}, "itineraryCreator": {"id": "itineraryCreator", "name": "Itinerary Creation Agent", "description": "Generates a detailed, day-by-day vacation itinerary based on a structured plan.", "role": "You are a travel planning expert. You will receive 'vacationPlan' which contains the structured vacation details. Your job is to take this structured plan and create a 5-day itinerary. For each day, suggest a few activities, dining options (based on the provided budget and preferences), and a general overview of the day. Be creative and helpful, but stay within the provided constraints. You MUST use the search tool to find relevant information for activities and dining. Use the search tool by writing [TOOL: webSearch({\"query\": \"your search query here\"})] or [TOOL: webSearch(\"simple query\")].", "goals": ["Create a detailed, day-by-day itinerary.", "Ensure the itinerary aligns with all provided preferences and budget.", "Include suggestions for activities, dining, and accommodations.", "Utilize the webSearch tool to gather necessary information."], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.7, "maxOutputTokens": 2048}, "tools": {"webSearch": "webSearch"}}}, "team": {"vacationTeam": {"name": "Vacation Planning Team", "description": "A team of two agents working together to plan a vacation.", "agents": ["goalPlanner", "itineraryCreator"], "jobs": {"planGoals": {"description": "Convert vacation preferences into structured plan", "agent": "goalPlanner", "inputs": {"vacationIdea": "A relaxing beach vacation in Maui for 5 days.", "preferences": "Stay in a quiet resort, go snorkeling, and try local Hawaiian food.", "travelDates": "Dec 10-15, 2025", "budget": "Mid-range ($2000-$3000)", "numTravelers": 2}}, "createItinerary": {"description": "Generate a detailed itinerary", "agent": "itineraryCreator", "inputs": {"vacationPlan": "{{planGoals.output}}"}}}, "workflow": ["planGoals", "createItinerary"]}}, "brief": {"maui-trip-001": {"vacationIdea": "A relaxing beach vacation in Maui for 5 days.", "preferences": "Stay in a quiet resort, go snorkeling, and try local Hawaiian food.", "travelDates": "Dec 10-15, 2025", "budget": "Mid-range ($2000-$3000)", "numTravelers": 2}}}