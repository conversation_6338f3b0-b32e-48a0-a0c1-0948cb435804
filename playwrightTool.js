import { chromium } from 'playwright';

/**
 * A utility class to simplify common Playwright browser interactions.
 */
class PlaywrightTool {
    constructor() {
        this.browser = null;
        this.page = null;
    }

    /**
     * Initializes the Playwright browser and a new page.
     * @param {Object} [options] - Optional parameters for `chromium.launch()`.
     */
    async initialize(options = { headless: false }) {
        try {
            console.log("Initializing Playwright...");
            this.browser = await chromium.launch(options);
            this.page = await this.browser.newPage();
            console.log("Playwright initialized.");
        } catch (error) {
            console.error("Error initializing Playwright:", error);
            throw error;
        }
    }

    /**
     * Navigates the page to a specified URL.
     * @param {string} url - The URL to navigate to.
     */
    async navigateTo(url) {
        if (!this.page) {
            await this.initialize();
        }
        try {
            console.log(`Navigating to ${url}...`);
            await this.page.goto(url, { waitUntil: 'domcontentloaded' });
            console.log(`Successfully navigated to ${url}.`);
        } catch (error) {
            console.error(`Error navigating to ${url}:`, error);
            throw error;
        }
    }

    /**
     * Fills a form field with the specified data.
     * @param {string} selector - The selector for the input element.
     * @param {string} data - The data to fill.
     * @param {Object} [options] - Optional parameters.
     * @param {boolean} [options.handleAutocomplete=false] - If true, waits after filling and optionally presses Enter.
     * @param {number} [options.waitAfterFill=1000] - Milliseconds to wait after filling if handleAutocomplete is true.
     * @param {boolean} [options.pressEnter=false] - If true, presses Enter after waiting when handleAutocomplete is true.
     */
    async fillForm(selector, data, options = {}) {
        if (!this.page) {
            await this.initialize();
        }
        try {
            console.log(`Filling form field "${selector}" with data "${data}"`);
            await this.page.waitForSelector(selector, { state: 'visible', timeout: 15000 });
            await this.page.fill(selector, data);

            if (options.handleAutocomplete) {
                const waitTime = options.waitAfterFill || 1000;
                await this.page.waitForTimeout(waitTime);
                if (options.pressEnter) {
                    await this.page.press(selector, 'Enter');
                }
            }
            console.log("Form field filled successfully.");
        } catch (error) {
            console.error(`Error filling form field with selector "${selector}":`, error);
            throw error;
        }
    }

    /**
     * Selects a date from a calendar date picker by navigating months if necessary.
     * This function is specifically adapted to handle the dynamic aria-labels on Kayak's site.
     * @param {string} datePickerTriggerSelector - The selector for the element that opens the date picker.
     * @param {string} dateToSelect - The full date string to select (e.g., "August 15, 2025").
     * @param {string} [nextMonthButtonSelector='[aria-label="Next month"]'] - The selector for the "next month" button.
     * @param {string} [prevMonthButtonSelector='[aria-label="Previous month"]'] - The selector for the "previous month" button.
     */
    async selectDate(datePickerTriggerSelector, dateToSelect, nextMonthButtonSelector = '[aria-label="Next month"]', prevMonthButtonSelector = '[aria-label="Previous month"]') {
        if (!this.page) {
            await this.initialize();
        }
        try {
            console.log(`Clicking date picker trigger: ${datePickerTriggerSelector}`);
            await this.clickElement(datePickerTriggerSelector);
            await this.page.waitForTimeout(1000 + Math.random() * 500); // Delay after clicking trigger

            // Wait for the entire dropdown content to be visible first for stability
            const dropdownContentSelector = 'div.sGVi.sGVi-dropdown-content';
            await this.page.waitForSelector(dropdownContentSelector, { state: 'visible', timeout: 20000 });
            console.log('Date picker dropdown content is visible.');
            await this.page.waitForTimeout(1500 + Math.random() * 500); // Give extra time for internal rendering

            await this.page.screenshot({ path: 'debug_calendar_visible.png' });
            console.log('Screenshot taken: debug_calendar_visible.png');

            const targetDate = new Date(dateToSelect);
            const targetMonth = targetDate.getMonth();
            const targetYear = targetDate.getFullYear();
            const day = targetDate.getDate();

            let monthFound = false;
            let attempts = 0;
            const maxMonthNavigationAttempts = 24;

            while (!monthFound && attempts < maxMonthNavigationAttempts) {
                const monthHeaderSelector = 'caption.w0lb-month-name, .g_wN-month-name';
                await this.page.waitForSelector(monthHeaderSelector, { state: 'visible', timeout: 10000 });
                const monthHeaders = await this.page.$$eval(monthHeaderSelector, elements => elements.map(el => el.textContent.trim()));

                console.log(`Current displayed months: ${monthHeaders.join(', ')}`);

                if (monthHeaders.length > 0) {
                    for (const headerText of monthHeaders) {
                        try {
                            const parsedHeaderDate = new Date(headerText + ' 1');
                            if (parsedHeaderDate.getMonth() === targetMonth && parsedHeaderDate.getFullYear() === targetYear) {
                                monthFound = true;
                                console.log(`Target month "${headerText}" found.`);
                                break;
                            }
                        } catch (e) {
                            console.warn(`Could not parse month header "${headerText}". Error: ${e.message}`);
                        }
                    }
                }

                if (monthFound) {
                    break;
                }

                if (monthHeaders.length > 0) {
                    const firstDisplayedMonthDate = new Date(monthHeaders[0] + ' 1');
                    const firstDisplayedMonth = firstDisplayedMonthDate.getMonth();
                    const firstDisplayedYear = firstDisplayedMonthDate.getFullYear();

                    if (targetYear < firstDisplayedYear || (targetYear === firstDisplayedYear && targetMonth < firstDisplayedMonth)) {
                        console.log('Navigating to previous month...');
                        await this.clickElement(prevMonthButtonSelector);
                    } else {
                        console.log('Navigating to next month...');
                        await this.clickElement(nextMonthButtonSelector);
                    }
                } else {
                    console.warn("Still no month headers found, attempting to click next month as a fallback.");
                    await this.clickElement(nextMonthButtonSelector);
                }

                await this.page.waitForTimeout(750 + Math.random() * 250);
                attempts++;
            }

            if (!monthFound) {
                throw new Error(`Target month ${targetDate.toLocaleString('default', { month: 'long' })} ${targetYear} not found after ${maxMonthNavigationAttempts} attempts.`);
            }

            await this.page.screenshot({ path: 'debug_month_found.png' });
            console.log('Screenshot taken: debug_month_found.png');

            // --- CRITICAL REVISION: Adapt to dynamic aria-label using 'contains' ---
            let dateClicked = false;
            const maxClickAttempts = 3;
            for (let i = 0; i < maxClickAttempts && !dateClicked; i++) {
                console.log(`Attempt ${i + 1} to click date "${dateToSelect}"`);
                await this.page.screenshot({ path: `debug_before_date_click_attempt_${i + 1}.png` });
                try {
                    // Method 1: Use getByRole with 'contains' match for the name
                    // The 'name' here is derived from aria-label.
                    const dateButtonLocator = this.page.getByRole('button', { name: dateToSelect }); // Removed 'exact: true'
                    await dateButtonLocator.waitFor({ state: 'visible', timeout: 5000 });
                    await dateButtonLocator.hover({ timeout: 2000 });
                    await this.page.waitForTimeout(200 + Math.random() * 100);
                    await dateButtonLocator.click({ force: true, timeout: 5000 });
                    dateClicked = true;
                    console.log(`Successfully selected date via getByRole (contains name): ${dateToSelect}`);
                } catch (e1) {
                    console.warn(`getByRole (contains name) failed: ${e1.message}. Trying CSS selector with attribute contains.`);
                    try {
                        // Method 2: CSS selector with attribute contains for aria-label
                        const genericDateSelector = `[aria-label*="${dateToSelect}"]`; // Using *= for contains
                        await this.page.waitForSelector(genericDateSelector, { state: 'visible', timeout: 5000 });
                        const genericElement = await this.page.$(genericDateSelector);
                        if (genericElement) {
                            await genericElement.hover({ timeout: 2000 });
                            await this.page.waitForTimeout(200 + Math.random() * 100);
                            await genericElement.click({ force: true, timeout: 5000 });
                            dateClicked = true;
                            console.log(`Successfully selected date via CSS selector (aria-label contains): ${dateToSelect}`);
                        } else {
                            throw new Error(`Generic selector "${genericDateSelector}" not found.`);
                        }
                    } catch (e2) {
                        console.warn(`CSS selector (aria-label contains) failed: ${e2.message}. Trying native DOM click by text content.`);
                        try {
                            // Method 3: Native DOM click by text content (day number) within visible calendar
                            await this.page.evaluate((dayNumber, dateToSelectFull) => {
                                const calendarContainer = document.querySelector('div.sGVi.sGVi-dropdown-content');
                                if (!calendarContainer) {
                                    throw new Error("Calendar dropdown content not found for native click.");
                                }
                                // Find elements within the calendar that contain the day number and are interactive
                                const dateElements = Array.from(calendarContainer.querySelectorAll('div[role="button"], [aria-label]'))
                                    .filter(el => {
                                        const text = el.textContent.trim();
                                        const ariaLabel = el.getAttribute('aria-label');
                                        // Prioritize full aria-label match if possible, otherwise text content
                                        return (ariaLabel && ariaLabel.includes(dateToSelectFull)) || text === dayNumber.toString();
                                    });

                                if (dateElements.length > 0) {
                                    dateElements[0].click(); // Click the first match
                                } else {
                                    throw new Error(`Date element for day ${dayNumber} or full date "${dateToSelectFull}" not found by text content/aria-label for native click.`);
                                }
                            }, day, dateToSelect);
                            dateClicked = true;
                            console.log(`Successfully selected date via native DOM click (text content/aria-label): ${dateToSelect}`);
                        } catch (e3) {
                            console.error(`Native DOM click failed: ${e3.message}`);
                            if (i === maxClickAttempts - 1) {
                                await this.page.screenshot({ path: 'debug_date_click_ultimate_failure.png' });
                                console.log('Screenshot taken: debug_date_click_ultimate_failure.png');
                                throw e3;
                            }
                        }
                    }
                }
                if (!dateClicked) {
                    await this.page.waitForTimeout(1000 + Math.random() * 500);
                }
            }

            if (!dateClicked) {
                throw new Error(`Failed to click date "${dateToSelect}" after ${maxClickAttempts} attempts.`);
            }

            await this.page.waitForTimeout(1000 + Math.random() * 500);

            console.log(`Successfully selected date: ${dateToSelect}`);

        } catch (error) {
            console.error(`Error selecting date "${dateToSelect}" with trigger "${datePickerTriggerSelector}":`, error);
            throw error;
        }
    }

    /**
     * Clicks a specific element on the page.
     * @param {string} selector - The selector for the element to click.
     */
    async clickElement(selector) {
        if (!this.page) {
            await this.initialize();
        }
        try {
            console.log(`Clicking element with selector: ${selector}`);
            // Wait for the element to be visible and then click it forcefully
            await this.page.waitForSelector(selector, { state: 'visible', timeout: 15000 });
            await this.page.click(selector, { force: true });
            await this.page.waitForTimeout(500); // Add a small delay after click
            console.log(`Element clicked: ${selector}`);
        } catch (error) {
            console.error(`Error clicking element with selector "${selector}":`, error);
            throw error;
        }
    }

    /**
     * Takes a screenshot of the current page.
     * @param {string} path - The file path to save the screenshot.
     */
    async takeScreenshot(path) {
        if (!this.page) {
            await this.initialize();
        }
        try {
            console.log(`Taking screenshot at path: ${path}`);
            await this.page.screenshot({ path: path });
            console.log(`Screenshot saved to ${path}`);
        } catch (error) {
            console.error(`Error taking screenshot at path "${path}":`, error);
            throw error;
        }
    }

    /**
     * Closes the browser.
     */
    async close() {
        if (this.browser) {
            try {
                console.log("Closing browser...");
                await this.browser.close();
                console.log("Browser closed.");
            } catch (error) {
                console.error("Error closing browser:", error);
            } finally {
                this.browser = null;
                this.page = null;
            }
        }
    }
}

/**
 * Searches for flights on Kayak.
 * @param {string} origin - The departure airport or city.
 * @param {string} destination - The arrival airport or city.
 * @param {string} departureDate - The departure date (e.g., "August 15, 2025").
 * @param {string} returnDate - The return date (e.g., "August 22, 2025").
 * @returns {Promise<Array<Object>>} - A promise that resolves to an array of flight search results.
 */
async function kayakFlightSearch(origin, destination, departureDate, returnDate) {
    const tool = new PlaywrightTool();
    const results = [];

    try {
        await tool.initialize({ headless: true }); // Run headless for automation
        await tool.navigateTo('https://www.kayak.com/flights');

        // --- Fill Origin ---
        // Selector for origin input might change, this is a common one.
        const originSelector = 'input[placeholder="Where from?"]';
        await tool.fillForm(originSelector, origin, { handleAutocomplete: true, pressEnter: true });
        await tool.page.waitForTimeout(1000); // Wait for suggestions to settle

        // --- Fill Destination ---
        // Selector for destination input might change.
        const destinationSelector = 'input[placeholder="Where to?"]';
        await tool.fillForm(destinationSelector, destination, { handleAutocomplete: true, pressEnter: true });
        await tool.page.waitForTimeout(1000); // Wait for suggestions to settle

        // --- Select Departure Date ---
        // Selector for the departure date input field.
        const departureDateSelector = 'div[data-placeholder="Depart"]';
        await tool.selectDate(departureDateSelector, departureDate);
        await tool.page.waitForTimeout(1000); // Wait for calendar to close/update

        // --- Select Return Date ---
        // Selector for the return date input field.
        const returnDateSelector = 'div[data-placeholder="Return"]';
        await tool.selectDate(returnDateSelector, returnDate);
        await tool.page.waitForTimeout(1000); // Wait for calendar to close/update

        // --- Click Search Button ---
        // Selector for the search button.
        const searchButtonSelector = 'button[type="submit"]';
        await tool.clickElement(searchButtonSelector);

        // --- Wait for results page to load ---
        // A common indicator for results page is the presence of flight result elements.
        // This selector might need adjustment based on Kayak's actual result structure.
        const flightResultSelector = '.result-inner'; // Example selector, may need refinement
        await tool.page.waitForSelector(flightResultSelector, { state: 'visible', timeout: 30000 });
        console.log("Flight search results page loaded.");

        // --- Extract Flight Data ---
        // This is a placeholder for extracting flight data.
        // The actual selectors for prices, times, airlines, etc., will need to be determined by inspecting Kayak's HTML.
        const flightElements = await tool.page.$$(flightResultSelector);
        for (const flightElement of flightElements) {
            const priceElement = await flightElement.$('.price'); // Example price selector
            const price = priceElement ? await priceElement.textContent() : 'N/A';

            const airlineElement = await flightElement.$('.airline-name'); // Example airline selector
            const airline = airlineElement ? await airlineElement.textContent() : 'N/A';

            const departureTimeElement = await flightElement.$('.depart-time'); // Example departure time selector
            const departureTime = departureTimeElement ? await departureTimeElement.textContent() : 'N/A';

            const arrivalTimeElement = await flightElement.$('.arrival-time'); // Example arrival time selector
            const arrivalTime = arrivalTimeElement ? await arrivalTimeElement.textContent() : 'N/A';

            results.push({
                price: price.trim(),
                airline: airline.trim(),
                departureTime: departureTime.trim(),
                arrivalTime: arrivalTime.trim(),
            });

            if (results.length >= 5) break; // Limit to first 5 results for brevity
        }

        console.log(`Found ${results.length} flight results.`);

    } catch (error) {
        console.error("Error during Kayak flight search:", error);
        // Optionally take a screenshot on error
        try {
            await tool.takeScreenshot('debug_kayak_search_error.png');
        } catch (screenshotError) {
            console.error("Failed to take error screenshot:", screenshotError);
        }
        throw error; // Re-throw the error to be caught by the caller
    } finally {
        await tool.close();
    }

    return results;
}

export { PlaywrightTool, kayakFlightSearch };