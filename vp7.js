// vp7.js - Orchestrator for the vacation planning workflow defined in vp7.json

import { AgentFactory } from './AgentFactory.js';
import { TeamFactory } from './TeamFactory.js';
import { AgencyFactory } from './AgencyFactory.js';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv'; // For loading environment variables
import fs from 'fs/promises'; // For file system operations
import open from 'open'; // For opening the results HTML file

// Import tools used in vp7.json (assuming these files exist in your setup)
import { webSearch } from './search_tool.js';
import { dateTimeTool } from './datetime.js';
import { calculatorTool as calculator } from './calculator.js';
import { kayakFlightSearch } from './playwrightTool.js';

// Load environment variables from a .env file.
dotenv.config();

async function main() {
  try {
    const GEMINI_API_KEY = process.env.GEMINI_API_KEY;

    if (!GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY environment variable is not set. Please create a .env file with GEMINI_API_KEY=your_key_here');
    }

    // Initialize the AgentFactory with the Gemini API key.
    const agentFactory = new AgentFactory({
      defaultProvider: 'gemini',
      apiKeys: {
        gemini: GEMINI_API_KEY
      }
    });

    // Register tools used by the agents in vp7.json.
    const travelSearchTool = agentFactory.registerTool('travelSearch', webSearch, {
      description: 'Search for travel-related information including destinations, accommodations, and activities',
      isAsync: true,
      timeout: 15000
    });

    const datetimeToolInstance = agentFactory.registerTool('datetime', dateTimeTool, {
      description: 'Get current date/time information and perform date calculations',
      isAsync: false,
      timeout: 5000
    });

    const calculatorToolInstance = agentFactory.registerTool('calculator', calculator, {
      description: 'Perform mathematical calculations for budgeting and planning',
      isAsync: false,
      timeout: 5000
    });

    const kayakFlightSearchToolInstance = agentFactory.registerTool('kayakFlightSearch', kayakFlightSearch, {
      description: 'Searches for the cheapest flight details on Kayak.com',
      isAsync: true,
      timeout: 30000 // Increased timeout for browser operations
    });

    // Add validators and monitors to the tools.
    travelSearchTool.addValidator((params) => {
      if (!params.query || params.query.length < 3) {
        throw new Error('Search query must be at least 3 characters long');
      }
    });

    travelSearchTool.addMonitor((toolName, params, duration, success, result, error) => {
      console.log(`Tool ${toolName} executed in ${duration}ms, success: ${success}`);
      if (error) console.log(`Error: ${error.message}`);
    });

    kayakFlightSearchToolInstance.addValidator((params) => {
      if (!params.departureLocation || !params.destinationLocation || !params.departureDate || !params.returnDate) {
        throw new Error('kayakFlightSearch requires departureLocation, destinationLocation, departureDate, and returnDate');
      }
    });

    kayakFlightSearchToolInstance.addMonitor((toolName, params, duration, success, result, error) => {
      console.log(`Tool ${toolName} executed in ${duration}ms, success: ${success}`);
      if (error) console.log(`Error: ${error.message}`);
    });


    // Create the team and agency factories, passing the agentFactory instance.
    const teamFactory = new TeamFactory({ agentFactory });
    const agencyFactory = new AgencyFactory({
      teamFactory,
      agentFactory
    });

    // Get the directory name of the current module.
    const __dirname = path.dirname(fileURLToPath(import.meta.url));

    // Define the path to the vp7 configuration file.
    const configPath = path.join(__dirname, 'vp7.json');

    // Load the agency configuration from the JSON file.
    const agency = await agencyFactory.loadAgencyFromFile(configPath);

    console.log('Vacation Planner Agency (vp7.json) loaded successfully:');
    console.log('- name:', agency.name);
    console.log('- team:', Object.keys(agency.team));
    console.log('- brief:', Object.keys(agency.brief));

    // The job to execute is 'st-pete-clearwater-trip-001'.
    const jobId = 'st-pete-clearwater-trip-001';
    const teamName = 'vacationTeam';

    if (!agency.brief[jobId]) {
        throw new Error(`Brief with ID "${jobId}" not found in vp7.json`);
    }
    if (!agency.team[teamName]) {
        throw new Error(`Team with name "${teamName}" not found in vp7.json`);
    }

    const brief = agency.brief[jobId];

    // Assign the job to the team
    console.log(`Assigning job "${jobId}" to "${teamName}" (type: team)...`);
    agency.assignJob(jobId, teamName, 'team');

    // Execute the workflow
    console.log('Executing job...');
    console.log('Using initial inputs for the workflow:');
    console.log('- brief:', JSON.stringify(brief, null, 2));

    const results = await agency.execute(jobId, brief);

    console.log('Job execution completed. Results received.');

    console.log('\n=== VACATION PLANNING RESULTS ===\n');

    console.log('Results object contains keys:', Object.keys(results));


    // Display the results for each step in the workflow as HTML.
    let htmlContent = `<!DOCTYPE html>\n<html lang="en">\n<head>\n<meta charset="UTF-8">\n<title>Vacation Planning Results</title>\n<style>\nbody { font-family: Arial, sans-serif; margin: 2em; background: #f9f9f9; }\nh1 { color: #2a7ae2; }\nh2 { color: #333; border-bottom: 1px solid #eee; padding-bottom: 0.2em; }\npre { background: #f4f4f4; padding: 1em; border-radius: 6px; overflow-x: auto; }\n.warning { color: #b00; font-weight: bold; }\n</style>\n</head>\n<body>\n<h1>Vacation Planning Results</h1>\n<p><strong>Results object contains keys:</strong> [ ${Object.keys(results).join(', ')} ]</p>\n`;

    const team = agency.team[teamName];
    for (const jobName of team.workflow) {
      if (results[jobName]) {
        const resultText = typeof results[jobName] === 'object' ? JSON.stringify(results[jobName], null, 2) : results[jobName];
        console.log(`${jobName.replace(/([A-Z])/g, ' $1').trim()} (length: ${resultText.length} characters):`);
        console.log(resultText);
        console.log('\n');
        htmlContent += `<h2>${jobName.replace(/([A-Z])/g, ' $1').trim()} <span style='font-size:0.7em;color:#888;'>(length: ${resultText.length} characters)</span></h2>\n<pre>${resultText}</pre>\n`;
      } else {
        console.log(`WARNING: No ${jobName} results found!`);
        htmlContent += `<h2>${jobName.replace(/([A-Z])/g, ' $1').trim()}</h2>\n<p class='warning'>WARNING: No results found for this step.</p>\n`;
      }
    }
    htmlContent += `</body>\n</html>`;

    // Save the results to an HTML file.
    const resultsFilePath = path.join(__dirname, 'vacation-results-st-pete.html');
    await fs.writeFile(resultsFilePath, htmlContent, 'utf8');
    console.log(`Results saved to ${resultsFilePath}`);
    // Automatically open the results file using npm open
    await open(resultsFilePath);

    console.log('Vacation planning workflow completed successfully!');

  } catch (error) {
    console.error('Error during workflow execution:', error);
    if (error.response && error.response.data) {
      console.error('API Error Details:', error.response.data);
    }
  }
}

// Execute the main function.
main();
