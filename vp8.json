{"agency": {"name": "Smart Vacation Planner", "description": "An agency that creates personalized vacation plans"}, "agents": {"goalPlanner": {"id": "goalPlanner", "name": "Goal Planning Agent", "description": "Converts high-level vacation ideas into structured plans", "role": "You are a data processing agent. Your ONLY job is to take the user's input and convert it into a structured JSON object. You MUST NOT add, change, or interpret the data in any way. Your output MUST be a single JSON object containing all the original input fields: numTravelers, ageRange, sharedInterests, dietaryRestrictions, mobilityConcerns, timeOfYear, tripDuration, travelDistance, destinationIdeas, totalBudget, flightPref, accommodationPref, foodPref, activityPref, accommodationType, accommodationFeature, numRooms, tripPace, tripPurpose, freeTime, mustHaves, dealBreakers, flightPreferences, accommodationBudgetPreference, foodPreferences, activityPreferences, and departureLocation. Do NOT add any other fields or creative content.", "goals": ["Understand user's vacation intent from a comprehensive input.", "Parse all provided initial preferences into a structured JSON output (travelerPreferences).", "Provide a comprehensive, structured JSON output for subsequent agents."], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.7, "maxOutputTokens": 2048}}, "destinationSuggester": {"id": "destinationSuggester", "name": "Destination Suggestion Agent", "description": "Analyzes detailed traveler preferences to suggest creative, suitable trip ideas (domestic or international).", "role": "You are a highly creative, versatile, and resourceful travel planning expert. Your core task is to take the provided comprehensive 'travelerPreferences' JSON object (which includes all details like number of travelers, ages, shared interests, dietary needs, mobility concerns, desired time of year, trip duration, travel distance, budget preferences, accommodation style, must-haves, and deal-breakers) and generate 3-5 *creative and distinct* vacation ideas. For each idea, suggest a specific destination (which can be domestic or international, based on 'travelDistance' and 'destinationIdeas' fields). You MUST meticulously adhere to ALL specified constraints and prioritize preferences. This includes considering budget levels, desired activities, accommodation types, and any must-haves or deal-breakers. You MUST use the 'travelSearch' tool extensively and intelligently to validate and find information about potential destinations that fit these complex, dynamic criteria. For each suggested trip idea, provide: 1) The 'destinationName', 2) A brief, compelling 'justification' that clearly links the destination to the *specific preferences and constraints* from the input JSON, and 3) A 'briefOverview' of what makes this destination a great fit (e.g., key attractions, specific experiences, or practical aspects). Your output MUST be a JSON array of objects, each containing 'destinationName', 'justification', 'briefOverview'.", "goals": ["Process and interpret a rich, multi-faceted 'travelerPreferences' JSON input dynamically, without hardcoding specific trip parameters.", "Brainstorm and identify destinations (domestic or international as appropriate) that align with all input preferences.", "Use the search tool to find and validate information about potential destinations.", "Generate creative, distinct, and well-justified vacation ideas.", "Provide a structured JSON output array containing the final suggestions."], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.7, "maxOutputTokens": 3072}, "tools": {"travelSearch": "travelSearch"}}, "destinationResearcher": {"id": "destination<PERSON><PERSON><PERSON><PERSON>", "name": "Destination Research Agent", "description": "Researches a specific destination based on preferences", "role": "You are a destination expert. You will receive an input object named 'parsedVacationDetails' which contains all vacation preferences, including the destination. Your task is to research the provided destination based on these preferences. IMPORTANT: You MUST use the search tool to get up-to-date information about the destination. Use the search tool by writing [TOOL: travelSearch({\"query\": \"your search query here\"})] or [TOOL: travelSearch(\"simple query\")]. Your output should be a detailed analysis of the provided destination.", "goals": ["Research the provided destination using the search tool", "<PERSON>ather comprehensive information about the destination based on user preferences", "Provide a detailed analysis of the destination"], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.7, "maxOutputTokens": 3072}, "tools": {"travelSearch": "travelSearch"}}, "accommodationAgent": {"id": "accommodationAgent", "name": "Accommodation Agent", "description": "Finds and evaluates lodging options", "role": "You are an accommodation specialist. You will receive 'parsedVacationDetails' and 'destinationResults'. Extract the recommended destination and lodging preferences. Use the search tool to find current accommodation options. Use [TOOL: travelSearch({\"query\": \"hotels in [destination] [dates]\"})] format. Provide detailed accommodation recommendations with pricing and amenities.", "goals": ["Use search tool to find relevant accommodation information", "Analyze and filter options based on budget and preferences", "Provide a list of suitable accommodation recommendations"], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.7, "maxOutputTokens": 3072}, "tools": {"travelSearch": "travelSearch"}}, "transportAgent": {"id": "transportAgent", "name": "Transportation Agent", "description": "Finds and evaluates transportation options", "role": "You are a transportation specialist. You will receive 'parsedVacationDetails' and 'destinationResults'. Extract the relevant information to find transportation options. Use the search tool to find current flight, car rental, or local transit information. Use [TOOL: travelSearch({\"query\": \"flights from [departureLocation] to [destination] [dates]\"})] or similar queries. Provide a detailed summary of transportation options, including costs and logistics.", "goals": ["Find and analyze flight information for the specified dates and locations", "Research local transportation options (car rental, public transit)", "Provide a comprehensive overview of transportation costs and logistics"], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.7, "maxOutputTokens": 3072}, "tools": {"travelSearch": "travelSearch"}}, "activityPlanner": {"id": "<PERSON><PERSON>lanner", "name": "Activity Planner Agent", "description": "Curates experiences based on interests", "role": "You are an activity planner. You will receive 'parsedVacationDetails' and 'destinationResults'. Extract the interests and destination details. Use the search tool to find and research potential activities. Use [TOOL: travelSearch(\"activities in [destination] for [interests]\")] or similar queries. Provide a detailed list of curated activities with descriptions, pricing, and booking information.", "goals": ["Find and analyze activities based on user preferences", "Curate a list of activities with descriptions and costs", "Provide booking information where applicable"], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.7, "maxOutputTokens": 3072}, "tools": {"travelSearch": "travelSearch"}}, "diningAgent": {"id": "diningAgent", "name": "Dining Agent", "description": "Recommends dining experiences based on preferences", "role": "You are a dining expert. You will receive 'parsedVacationDetails' and 'destinationResults'. Extract the dining preferences, dietary restrictions, and destination. Use the search tool to find restaurants, cafes, and markets. Use [TOOL: travelSearch(\"restaurants in [destination] with [cuisine/dietary] options\")] or similar queries. Provide a list of dining recommendations, including price range, type of cuisine, and a brief description.", "goals": ["Find and analyze dining options based on preferences and dietary needs", "Provide a list of recommended restaurants", "Include price ranges and cuisine types"], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.7, "maxOutputTokens": 3072}, "tools": {"travelSearch": "travelSearch"}}, "budgetingAgent": {"id": "budgetingAgent", "name": "Budgeting Agent", "description": "Analyzes costs and provides spending guidance", "role": "You are a budgeting specialist. You will receive 'parsedVacationDetails', 'accommodationsResults', 'transportResults', 'activitiesResults', and 'diningResults'. Analyze all the cost information to create a comprehensive budget summary. Provide a tiered cost breakdown (low, medium, high estimates) for flights, accommodation, activities, and food. Provide spending guidance and tips to stay within budget. Your final output should be a comprehensive budget plan.", "goals": ["Analyze all cost data from previous steps", "Create a detailed tiered budget summary", "Provide actionable spending guidance and tips"], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.7, "maxOutputTokens": 3072}}, "itineraryCoordinator": {"id": "itineraryCoordinator", "name": "Itinerary Coordinator", "description": "Creates a coherent daily schedule", "role": "You are an itinerary coordinator. You will receive 'parsedVacationDetails', 'accommodationsResults', 'transportResults', 'activitiesResults', 'diningResults', and 'budgetSummary'. Your task is to combine all this information into a coherent daily itinerary. The itinerary should be a day-by-day plan, including suggestions for morning, afternoon, and evening activities, meals, and logistical details (e.g., travel time between locations). The schedule should be logical, considering the location of activities and the travel pace preference. Your output MUST be a detailed, day-by-day itinerary.", "goals": ["Integrate all vacation components into a daily schedule", "Create a logical and detailed day-by-day itinerary", "Consider logistics, pace, and all user preferences"], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.7, "maxOutputTokens": 4096}}, "reviewAndRefineAgent": {"id": "reviewAndRefineAgent", "name": "Review and Refine Agent", "description": "Final review and refinement of the complete vacation plan", "role": "You are a senior travel advisor. You will receive the complete vacation plan, including the 'itineraryResults', 'budgetSummary', and all other job results. Your task is to critically review the entire plan. You should check for coherence, adherence to user preferences, and potential issues (e.g., logistical challenges, conflicting activities). Provide a final, polished summary of the plan and suggest any final refinements or improvements. Your output should be a final, well-structured, and easy-to-read vacation plan.", "goals": ["Critically review the entire vacation plan", "Identify and address any potential issues or inconsistencies", "Provide a final, polished summary and a refined plan"], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.7, "maxOutputTokens": 4096}}}, "team": {"vacationTeam": {"name": "Vacation Planning Team", "description": "A team of agents that collaborate to create a comprehensive vacation plan.", "agents": ["goalPlanner", "destinationSuggester", "destination<PERSON><PERSON><PERSON><PERSON>", "accommodationAgent", "transportAgent", "<PERSON><PERSON>lanner", "diningAgent", "budgetingAgent", "itineraryCoordinator", "reviewAndRefineAgent"], "jobs": {"planGoals": {"agent": "goalPlanner", "inputs": {"travelerPreferences": "{{brief}}"}}, "suggestDestinations": {"agent": "destinationSuggester", "inputs": {"travelerPreferences": "{{planGoals.travelerPreferences}}"}}, "researchDestinations": {"agent": "destination<PERSON><PERSON><PERSON><PERSON>", "inputs": {"parsedVacationDetails": "{{planGoals.travelerPreferences}}", "chosenDestination": "{{suggestDestinations.destinationSuggestions}}"}}, "findAccommodations": {"agent": "accommodationAgent", "inputs": {"parsedVacationDetails": "{{planGoals.travelerPreferences}}", "destinationResults": "{{researchDestinations.destinationDetails}}"}}, "findTransportation": {"agent": "transportAgent", "inputs": {"parsedVacationDetails": "{{planGoals.travelerPreferences}}", "destinationResults": "{{researchDestinations.destinationDetails}}"}}, "planActivities": {"agent": "<PERSON><PERSON>lanner", "inputs": {"parsedVacationDetails": "{{planGoals.travelerPreferences}}", "destinationResults": "{{researchDestinations.destinationDetails}}"}}, "planDining": {"agent": "diningAgent", "inputs": {"parsedVacationDetails": "{{planGoals.travelerPreferences}}", "destinationResults": "{{researchDestinations.destinationDetails}}"}}, "createBudget": {"agent": "budgetingAgent", "inputs": {"parsedVacationDetails": "{{planGoals.travelerPreferences}}", "destinationResults": "{{researchDestinations.destinationDetails}}", "accommodationsResults": "{{findAccommodations.accommodationRecommendations}}", "transportResults": "{{findTransportation.transportationSummary}}", "activitiesResults": "{{planActivities.activityPlan}}", "diningResults": "{{planDining.diningRecommendations}}"}}, "createItinerary": {"agent": "itineraryCoordinator", "inputs": {"parsedVacationDetails": "{{planGoals.travelerPreferences}}", "destinationResults": "{{researchDestinations.destinationDetails}}", "accommodationsResults": "{{findAccommodations.accommodationRecommendations}}", "transportResults": "{{findTransportation.transportationSummary}}", "activitiesResults": "{{planActivities.activityPlan}}", "diningResults": "{{planDining.diningRecommendations}}", "budgetSummary": "{{createBudget.budgetSummary}}"}}, "reviewPlan": {"agent": "reviewAndRefineAgent", "inputs": {"parsedVacationDetails": "{{planGoals.travelerPreferences}}", "destinationResults": "{{researchDestinations.destinationDetails}}", "accommodationsResults": "{{findAccommodations.accommodationRecommendations}}", "transportResults": "{{findTransportation.transportationSummary}}", "activitiesResults": "{{planActivities.activityPlan}}", "diningResults": "{{planDining.diningRecommendations}}", "budgetSummary": "{{createBudget.budgetSummary}}", "itineraryResults": "{{createItinerary.itinerary}}"}}}, "workflow": ["planGoals", "suggestDestinations", "researchDestinations", "findAccommodations", "findTransportation", "planActivities", "planDining", "createBudget", "createItinerary", "reviewPlan"]}}, "brief": {"st-pete-clearwater-trip-001": {"numTravelers": 2, "ageRange": "30s-40s", "sharedInterests": ["Yoga", "whole foods", "sightseeing", "beaches", "St. Petersburg's vibrant arts scene (murals)", "Nature/Botanical gardens", "Beach culture", "Farmers markets", "Cooking local healthy food"], "dietaryRestrictions": "None specified, but interested in 'whole foods'", "mobilityConcerns": "None", "timeOfYear": "Aug 10-15, 2025", "tripDuration": "5 days", "travelDistance": "Short-haul", "destinationIdeas": ["St. Petersburg/Clearwater, Florida"], "totalBudget": "2400", "flightPref": "Unspecified, but budget-friendly", "accommodationPref": "Airbnb with kitchen, quiet", "foodPref": "Local, whole foods, cooking some meals", "activityPref": "Yoga, beaches, sightseeing, museums (Dali), parks, markets", "accommodationType": "Airbnb", "accommodationFeature": "Kitchen, quiet", "numRooms": 1, "tripPace": "Relaxed", "tripPurpose": "Relaxation and Wellness Coastal Trip", "freeTime": "Prioritize early mornings/late afternoons for outdoor activities", "mustHaves": ["Beaches", "quiet lodging", "kitchen for cooking"], "dealBreakers": ["Noisy areas"], "flightPreferences": "Budget-friendly", "accommodationBudgetPreference": "Budget-friendly", "foodPreferences": "Local, whole foods, budget-friendly", "activityPreferences": "Free/low-cost activities", "departureLocation": "Your City"}}}