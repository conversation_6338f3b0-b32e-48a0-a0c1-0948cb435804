# Framework Tool Handling System

This framework provides a robust and flexible system for integrating and managing external tools within an agentic architecture. It allows agents to dynamically call functions and interact with external services, enhancing their capabilities beyond pure language generation.

## Core Architecture

### Tool Class (`Tool.js`)
The `Tool` class serves as the fundamental building block for defining any external capability. It encapsulates the tool's metadata and its executable function.

```javascript
// Example from Tool.js (conceptual)
class Tool {
    constructor({ name, description, func, isAsync = false, timeout = 5000 }) {
        this.name = name;
        this.description = description;
        this.func = func;
        this.isAsync = isAsync;
        this.timeout = timeout;
        this.validators = [];
        this.monitors = [];
    }

    addValidator(validatorFn) { /* ... */ }
    addMonitor(monitorFn) { /* ... */ }
    async execute(params) { /* ... */ }
}
```

### AgentFactory Tool Registration (`AgentFactory.js`)
Tools are registered globally with the `AgentFactory`. This factory manages a registry of available tools that can then be assigned to individual agents.

```javascript
// Example from vp3.js
const travelSearchTool = agentFactory.registerTool('travelSearch', webSearch, {
  description: 'Search for travel-related information...',
  isAsync: true,
  timeout: 15000
});

// Optional configuration can be omitted:
// const datetimeTool = agentFactory.registerTool('datetime', dateTimeTool);
```
The `registerTool` method accepts:
*   `toolName`: A unique identifier for the tool.
*   `toolInstanceOrFunction`: Either a `Tool` instance or a direct JavaScript function.
*   `config` (optional): An object to provide `description`, `isAsync`, `timeout`, and other custom properties for the tool.

### Agent-Tool Binding (`vp3.json` & `Agent.js`)
Agents declare which registered tools they can use within their configuration (e.g., `vp3.json`). The `Agent` class then receives these tools and makes them available for execution.

```json
// Example from vp3.json
"destinationResearcher": {
  "id": "destinationResearcher",
  "name": "Destination Research Agent",
  // ...
  "tools": {
    "travelSearch": "travelSearch" // Refers to a tool registered in AgentFactory
  }
}
```

## Key Features

### Tool Calling Patterns
The LLM's response is parsed to identify tool calls. The primary supported format is:

```
[TOOL: toolName(JSON_string_of_parameters)]
```
Example: `[TOOL: travelSearch({"query": "best beaches in Miami"})]`

The system also supports a JSON function calling format:
```json
{"tool": "toolName", "parameters": {"param1": "value1", "param2": "value2"}}
```

### Validation Layer
Tools can have validators attached to them to ensure input parameters meet specific criteria before execution. If validation fails, the tool call is prevented, and an error is logged.

```javascript
// Example from vp3.js
travelSearchTool.addValidator((params) => {
  if (!params.query || params.query.length < 3) {
    throw new Error('Search query must be at least 3 characters long');
  }
});
```

### Execution Monitoring
Monitors can be added to tools to log or track their execution, including duration, success status, and any errors. This is crucial for observability and debugging.

```javascript
// Example from vp3.js
travelSearchTool.addMonitor((toolName, params, duration, success, result, error) => {
  console.log(`Tool ${toolName} executed in ${duration}ms, success: ${success}`);
  if (error) console.log(`Error: ${error.message}`);
});
```

### Retry Mechanism
The `AdvancedToolHandler` includes a configurable retry mechanism for tool executions that fail. This enhances robustness against transient errors.

### Fallback Mechanism
If a tool execution ultimately fails after retries, the `AdvancedToolHandler` can be configured to either throw an error or provide a fallback message, allowing the agent to continue its process without halting.

## Implementation Details

### Tool Class Structure (`Tool.js`)
The `Tool` class manages the tool's function, its asynchronous nature, and optional timeout. It also holds arrays for `validators` and `monitors`. The `execute` method handles running the tool's function, applying validators, and invoking monitors.

### Agent Integration (`Agent.js`)
The `Agent` class is responsible for:
*   Receiving a set of `tools` in its configuration.
*   Utilizing a `ToolHandler` instance (by default, `AdvancedToolHandler`) to parse LLM responses and execute identified tool calls.
*   Passing the registered tools to its `llmProvider` so the LLM can be aware of available functions.
*   Emitting events (`toolCalled`, `toolCompleted`) during tool execution for external tracking.

### ToolHandler Classes (`Agent.js`)
The `Agent.js` file defines the core logic for handling tool calls:
*   **`ToolHandler` (Interface)**: Defines the `handleToolCalls` method that subclasses must implement.
*   **`DefaultToolHandler`**: A basic implementation that parses the `[TOOL: ...]` syntax.
*   **`AdvancedToolHandler`**: The default and more robust implementation, supporting multiple tool call formats (bracket and JSON), retries, and fallbacks. It uses an `EventEmitter` for internal events.

## Example Workflow

### Tool Definition and Registration
```javascript
// In vp3.js
import { webSearch } from './search_tool.js';
import { dateTimeTool } from './datetime.js';
import { calculatorTool as calculator } from './calculator.js';

// ... inside main function
const agentFactory = new AgentFactory({ /* ... */ });

const travelSearchTool = agentFactory.registerTool('travelSearch', webSearch, {
  description: 'Search for travel-related information...',
  isAsync: true,
  timeout: 15000
});

const datetimeTool = agentFactory.registerTool('datetime', dateTimeTool, {
  description: 'Get current date/time information...',
  isAsync: false,
  timeout: 5000
});

const calculatorToolInstance = agentFactory.registerTool('calculator', calculator, {
  description: 'Perform mathematical calculations...',
  isAsync: false,
  timeout: 5000
});

// Add validators and monitors (optional)
travelSearchTool.addValidator((params) => {
  if (!params.query || params.query.length < 3) {
    throw new Error('Search query must be at least 3 characters long');
  }
});
```

### Agent Configuration (e.g., `vp3.json`)
Agents are configured to use specific tools by referencing their registered names.

```json
"activityPlanner": {
  "id": "activityPlanner",
  "name": "Activity Planning Agent",
  "role": "You are an activity planning expert. ... Use search and datetime tools to find activities. Use [TOOL: travelSearch({\"query\": \"activities in [destination]\"})] and [TOOL: datetime({\"action\": \"get_current_date\"})] formats.",
  // ...
  "tools": {
    "travelSearch": "travelSearch",
    "datetime": "datetime"
  }
}
```

### Execution Flow
1.  An agent's `run` method is called with an input.
2.  The agent's `llmProvider` generates a response, which may include tool call syntax.
3.  The `Agent`'s `toolHandler` (an instance of `AdvancedToolHandler`) parses the LLM's response for tool calls.
4.  For each identified tool call:
    *   The tool's validators are run.
    *   The tool's underlying function (`func`) is executed (with retries if configured).
    *   Monitors are invoked to log the execution.
    *   The tool call syntax in the LLM's response is replaced with the formatted tool result.
5.  The final processed response (with tool results embedded) is returned by the agent.

## Best Practices

*   **Single Responsibility**: Each tool should perform a single, well-defined task.
*   **Statelessness**: Tools should ideally be stateless; any necessary state should be passed via parameters.
*   **Asynchronous Operations**: Mark tools performing I/O or long-running tasks as `isAsync: true` to ensure non-blocking execution.
*   **Clear Descriptions**: Provide descriptive `description` for each tool to help the LLM understand its purpose and when to use it.
*   **Robust Parameters**: Design tool functions to handle various parameter inputs gracefully, and use validators to enforce expected formats.

## Comparison with CrewAI's Tool Handling

Both this framework and CrewAI offer sophisticated approaches to tool handling in agentic systems, sharing many core principles while differing in implementation details and specific features.

