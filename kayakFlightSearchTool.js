// playwrightTool.js
import { chromium } from 'playwright';

class PlaywrightTool {
    constructor() {
        this.browser = null;
        this.page = null;
    }

    async initialize(options = {}) {
        try {
            this.browser = await chromium.launch(options);
            this.page = await this.browser.newPage();
        } catch (error) {
            console.error("Error initializing Playwright:", error);
            throw error; // Re-throw to indicate failure
        }
    }

    async navigateTo(url) {
        if (!this.page) {
            await this.initialize();
        }
        try {
            await this.page.goto(url);
        } catch (error) {
            console.error(`Error navigating to ${url}:`, error);
            throw error;
        }
    }

    /**
     * Fills a form field.
     * @param {string} selector - The selector for the input element.
     * @param {string} data - The data to fill.
     * @param {Object} [options] - Optional parameters.
     * @param {boolean} [options.handleAutocomplete=false] - If true, waits after filling and optionally presses Enter.
     * @param {number} [options.waitAfterFill=1000] - Milliseconds to wait after filling if handleAutocomplete is true.
     * @param {boolean} [options.pressEnter=false] - If true, presses Enter after waiting when handleAutocomplete is true.
     */
    async fillForm(selector, data, options = {}) {
        if (!this.page) {
            await this.initialize();
        }
        try {
            await this.page.fill(selector, data);

            // Handle autocomplete fields (common on travel sites like Kayak)
            if (options.handleAutocomplete) {
                const waitTime = options.waitAfterFill ?? 1000;
                await this.page.waitForTimeout(waitTime);
                if (options.pressEnter) {
                    await this.page.press(selector, 'Enter');
                }
            }
        } catch (error) {
            console.error(`Error filling form with selector "${selector}":`, error);
            throw error;
        }
    }

    async scrapeContent(selector) {
        if (!this.page) {
            await this.initialize();
        }
        try {
            // Check if multiple elements match the selector
            const elements = await this.page.$$(selector);
            if (elements.length > 1) {
                // If multiple elements, return an array of their text content
                const contents = [];
                for (const element of elements) {
                    contents.push(await element.textContent());
                }
                return contents;
            } else if (elements.length === 1) {
                // If a single element, return its text content
                return await elements[0].textContent();
            } else {
                // If no elements found
                return null;
            }
        } catch (error) {
            console.error(`Error scraping content with selector "${selector}":`, error);
            throw error;
        }
    }

    async scrapeAttribute(selector, attributeName) {
        if (!this.page) {
            await this.initialize();
        }
        try {
            const elements = await this.page.$$(selector);
            if (elements.length > 0) {
                const attributes = [];
                for (const element of elements) {
                    const attributeValue = await element.getAttribute(attributeName);
                    attributes.push(attributeValue);
                }
                return attributes.length === 1 ? attributes[0] : attributes;
            } else {
                return null;
            }
        } catch (error) {
            console.error(`Error scraping attribute "${attributeName}" with selector "${selector}":`, error);
            throw error;
        }
    }

    async scrapeAllText() {
        if (!this.page) {
            await this.initialize();
        }
        try {
            return await this.page.evaluate(() => document.body.innerText);
        } catch (error) {
            console.error("Error scraping all text from the page:", error);
            throw error;
        }
    }

    async clickElement(selector) {
        if (!this.page) {
            await this.initialize();
        }
        try {
            await this.page.click(selector);
        } catch (error) {
            console.error(`Error clicking element with selector "${selector}":`, error);
            throw error;
        }
    }

    async takeScreenshot(path) {
        if (!this.page) {
            await this.initialize();
        }
        try {
            await this.page.screenshot({ path: path });
        } catch (error) {
            console.error(`Error taking screenshot at path "${path}":`, error);
            throw error;
        }
    }

    async close() {
        if (this.browser) {
            try {
                await this.browser.close();
            } catch (error) {
                console.error("Error closing browser:", error);
            } finally {
                this.browser = null;
                this.page = null;
            }
        }
    }
}


/**
 * Searches Kayak.com for flights using the provided parameters.
 * @param {Object} params
 * @param {string} params.departureLocation - The origin city or airport.
 * @param {string} params.destinationLocation - The destination city or airport.
 * @param {string} params.departureDate - Departure date in YYYY-MM-DD format.
 * @param {string} params.returnDate - Return date in YYYY-MM-DD format.
 * @returns {Promise<Object>} - Summary of the search or scraped results.
 */

export async function kayakFlightSearch(params) {
    const tool = new PlaywrightTool();
    try {
        await tool.initialize({ headless: true });
        await tool.navigateTo('https://www.kayak.com/flights');
        await tool.page.waitForTimeout(5000);

        // Use working selectors from debug
        const originSelector = 'input[aria-label="Flight origin input"]';
        const destinationSelector = 'input[aria-label="Flight destination input"]';
        const searchButtonSelector = 'button[type="submit"]';

        // Fill origin
        await tool.fillForm(originSelector, params.departureLocation, { handleAutocomplete: true, pressEnter: true });
        await tool.page.waitForTimeout(1000);

        // Fill destination
        await tool.fillForm(destinationSelector, params.destinationLocation, { handleAutocomplete: true, pressEnter: true });
        await tool.page.waitForTimeout(1000);

        // Optionally, set dates (not implemented here; can be added if needed)

        // Click search
        await tool.clickElement(searchButtonSelector);

        // Wait for a robust selector for flight results
        // Try a few possible selectors for flight result cards
        let resultSelector = '[class*="resultWrapper"], [class*="Flights-Results-FlightResultItem"], [data-testid*="result"]';
        let results = null;
        try {
            await tool.page.waitForSelector(resultSelector, { timeout: 20000 });
            results = await tool.scrapeContent(resultSelector);
        } catch (e) {
            // If no results found, fallback to scraping all visible text
            results = await tool.scrapeAllText();
        }

        await tool.close();
        return {
            status: 'success',
            searched: {
                from: params.departureLocation,
                to: params.destinationLocation,
                depart: params.departureDate,
                return: params.returnDate
            },
            rawResults: results
        };
    } catch (error) {
        await tool.close();
        return { status: 'error', error: error.message };
    }
}

export default PlaywrightTool;