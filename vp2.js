// vp2.js - Orchestrator for a simplified vacation planning workflow.

import { AgentFactory } from './AgentFactory.js';
import { TeamFactory } from './TeamFactory.js';
import { AgencyFactory } from './AgencyFactory.js';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv'; // For loading environment variables
import fs from 'fs/promises'; // For file system operations
import open from 'open'; // For opening the results HTML file

// Import tools used in vp2.json
import { webSearch } from './search_tool.js'; // Assuming search_tool.js exports webSearch

// Load environment variables from a .env file.
dotenv.config();

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function main() {
  try {
    const GEMINI_API_KEY = process.env.GEMINI_API_KEY;

    if (!GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY environment variable is not set. Please create a .env file with GEMINI_API_KEY=your_key_here');
    }

    // Initialize the AgentFactory with the Gemini API key.
    const agentFactory = new AgentFactory({
      defaultProvider: 'gemini',
      apiKeys: { gemini: GEMINI_API_KEY },
    });

    // Register the webSearch tool
    agentFactory.registerTool('webSearch', webSearch);

    // Pass the agentFactory inside a configuration object to the TeamFactory constructor.
    const teamFactory = new TeamFactory({ agentFactory });

    // Create the AgencyFactory instance.
    const agencyFactory = new AgencyFactory({ teamFactory, agentFactory });

    // Create the agency from the vp2.json configuration.
    const agencyConfigPath = path.join(__dirname, 'vp2.json');
    const agency = await agencyFactory.loadAgencyFromFile(agencyConfigPath);
    
    // Get the brief ID from command line arguments or use a default
    const briefId = process.argv[2] || 'maui-trip-001';

    if (!agency.brief[briefId]) {
      throw new Error(`Brief with ID "${briefId}" not found in vp2.json`);
    }

    // Assign the job to the team
    console.log(`\nAssigning job "${briefId}" to "vacationTeam" (type: team)...\n`);
    agency.assignJob(briefId, 'vacationTeam', 'team');

    // Run the workflow
    console.log(`\nStarting workflow for brief ID: ${briefId}\n`);
    const results = await agency.execute(briefId);
    console.log('\n--- Workflow Execution Completed ---\n');

    // Generate a simple HTML report from the results.
    let htmlContent = `<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title>Simplified Vacation Planning Results</title>
<style>
body { font-family: Arial, sans-serif; margin: 2em; background: #f9f9f9; }
h1 { color: #2a7ae2; }
h2 { color: #333; border-bottom: 1px solid #eee; padding-bottom: 0.2em; }
pre { background: #f4f4f4; padding: 1em; border-radius: 6px; overflow-x: auto; }
</style>
</head>
<body>
<h1>Simplified Vacation Planning Results</h1>
<p><strong>Results object contains keys:</strong> [ ${Object.keys(results).join(', ')} ]</p>`;

    // Loop through the results and add them to the HTML content.
    const jobNames = [
      'planGoals',
      'createItinerary'
    ];

    for (const jobName of jobNames) {
      if (results[jobName]) {
        const resultText = typeof results[jobName] === 'object' ? JSON.stringify(results[jobName], null, 2) : results[jobName];
        console.log(`${jobName.replace(/([A-Z])/g, ' $1').trim()} (length: ${resultText.length} characters):`);
        console.log(resultText);
        console.log('\n');
        htmlContent += `<h2>${jobName.replace(/([A-Z])/g, ' $1').trim()} <span style='font-size:0.7em;color:#888;'>(length: ${resultText.length} characters)</span></h2>\n<pre>${resultText}</pre>\n`;
      } else {
        console.log(`WARNING: No ${jobName} results found!`);
        htmlContent += `<h2>${jobName.replace(/([A-Z])/g, ' $1').trim()}</h2>\n<p class='warning'>WARNING: No results found for this step.</p>\n`;
      }
    }
    htmlContent += `</body>\n</html>`;

    // Save the results to an HTML file.
    const resultsFilePath = path.join(__dirname, 'vacation-results.html');
    await fs.writeFile(resultsFilePath, htmlContent, 'utf8');
    console.log(`Results saved to ${resultsFilePath}`);

    // Open the results file in the default browser.
    await open(resultsFilePath);
  } catch (error) {
    console.error('An error occurred during the vacation planning workflow:', error);
  }
}

main();
