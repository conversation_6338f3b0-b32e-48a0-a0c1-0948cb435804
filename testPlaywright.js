import { PlaywrightTool } from './playwrightTool.js';

async function testFlightSearch() {
    const playwright = new PlaywrightTool();
    try {
        await playwright.initialize({ headless: false }); // Set to true for headless
        const url = 'https://www.kayak.com/flights';
        await playwright.navigateTo(url);

        // Define selectors
        const originInputSelector = 'input[aria-label="Flight origin input"]';
        const destinationInputSelector = 'input[aria-label="Flight destination input"]';
        const departureDateTriggerSelector = 'div[aria-label*="Depart"]';
        const returnDateTriggerSelector = 'div[aria-label*="Return"]';
        const searchButtonSelector = 'button[type="submit"]';

        // **NEW: Generic Modal Handler Function**
        // This function will attempt to close any common pop-ups
        async function handleModals() {
            // Selector for the "Edit your search" modal close button
            const editSearchModalCloseButton = 'div.dDYU-close[aria-label="Close"]';
            // Selector for a generic "No thanks" or "Close" button in other pop-ups (adjust as needed)
            const genericCloseButton = 'button[aria-label="No, thanks"]'; // Common on Kayak for feedback, newsletter etc.
            const genericXButton = 'button.Base-Modal-Header-CloseButton'; // Another common pattern for close 'X' buttons

            const selectorsToTry = [editSearchModalCloseButton, genericCloseButton, genericXButton];

            for (const selector of selectorsToTry) {
                try {
                    // Use a short timeout to check if the modal is present without waiting too long
                    await playwright.page.waitForSelector(selector, { state: 'visible', timeout: 3000 });
                    console.log(`Modal detected with selector: ${selector}. Attempting to close...`);
                    await playwright.clickElement(selector);
                    await playwright.page.waitForTimeout(1000); // Small pause for animation/dismissal
                    console.log('Modal dismissed.');
                    // If one modal is dismissed, there might be another, so we can re-check or just continue
                    // For now, let's assume one successful dismissal might be enough for this phase.
                    // If multiple modals can stack, you might need a loop here.
                    break; // Exit after successfully closing one
                } catch (error) {
                    // console.log(`No modal found for selector: ${selector}`);
                    // Continue to next selector if current one not found
                }
            }
        }

        // Handle pre-filled origin (like Orlando)
        const originClearButtonSelector = 'div.c_neb-item-close';
        console.log('Checking for pre-filled origin clear button...');
        try {
            await playwright.page.waitForSelector(originClearButtonSelector, { state: 'visible', timeout: 5000 });
            console.log('Pre-filled origin detected, clicking clear button...');
            await playwright.clickElement(originClearButtonSelector);
            await playwright.page.waitForTimeout(500); // Give time for the field to clear and update
            console.log('Pre-filled origin cleared.');
        } catch (error) {
            console.log('No pre-filled origin detected or clear button not found.');
        }

        // 1. Fill departure and destination
        console.log('Filling departure city (ATL)...');
        await playwright.fillForm(originInputSelector, 'Atlanta', { handleAutocomplete: true, pressEnter: true });
        await playwright.page.waitForTimeout(1000); // Give time for autocomplete to process

        console.log('Filling destination city (MIA)...');
        await playwright.fillForm(destinationInputSelector, 'Miami', { handleAutocomplete: true, pressEnter: true });
        await playwright.page.waitForTimeout(1000); // Give time for autocomplete to process

        // 2. Select Departure Date (August 15, 2025)
        console.log('Selecting departure date (August 15, 2025)...');
        await playwright.selectDate(departureDateTriggerSelector, 'August 15, 2025');
        await playwright.page.waitForTimeout(1000); // Wait for date to be selected

        // 3. Select Return Date (August 22, 2025)
        console.log('Selecting return date (August 22, 2025)...');
        await playwright.selectDate(returnDateTriggerSelector, 'August 22, 2025');
        await playwright.page.waitForTimeout(1000); // Wait for date to be selected

        // 4. Click Search button
        console.log('Clicking search button...');
        await playwright.clickElement(searchButtonSelector);

        // **IMPORTANT:** Call the modal handler immediately after search,
        // as the "Edit your search" modal is likely to appear here.
        console.log('Attempting to handle any immediate post-search modals...');
        await handleModals();

        // --- MODIFIED WAITING STRATEGY ---
        // Wait for the network to be idle after the search, indicating page content has likely loaded
        console.log('Waiting for network to settle after search...');
        await playwright.page.waitForLoadState('networkidle', { timeout: 60000 }); // Increase timeout for network idle
        console.log('Network idle state reached.');

        // Another check for modals after network idle, just in case
        console.log('Re-checking for modals after network idle...');
        await handleModals();


        console.log('Waiting for search results to fully load and become visible...');
        const resultsListWrapperSelector = '#flight-results-list-wrapper';
        try {
            // Increase timeout for the main wrapper visibility
            await playwright.page.waitForSelector(resultsListWrapperSelector, { state: 'visible', timeout: 45000 }); // Increased timeout
            console.log('Flight results list wrapper is visible.');
        } catch (error) {
            console.error('Error: Main flight results list not found within timeout. Cannot proceed with data extraction.');
            throw new Error('Flight results container not found.');
        }

        // Wait for at least one flight card to appear within the wrapper
        const firstFlightCardSelector = `${resultsListWrapperSelector} div.nrc6`;
        try {
            await playwright.page.waitForSelector(firstFlightCardSelector, { state: 'visible', timeout: 20000 }); // Slightly increased timeout
            console.log('First flight card is visible.');
        } catch (error) {
            console.warn('Warning: No flight cards found within timeout. Data extraction might fail.');
            // It's possible no flights are found, in which case the subsequent extraction will return empty.
            // You might want to add logic here to check for a "no results found" message.
        }
        await playwright.page.waitForTimeout(3000); // Add a small delay to ensure all content renders

        // --- EXTRACT FLIGHT DATA ---
        console.log('Extracting flight data...');

        const flightCardSelector = 'div.nrc6'; // Each individual flight result container

        const extractedFlightData = await playwright.page.$$eval(flightCardSelector, (flightCards) => {
            const data = [];
            for (const card of flightCards) {
                // Skip sponsored results that might be in a different structure if needed
                if (card.getAttribute('data-resultid') && card.getAttribute('data-resultid').includes('sponsored')) {
                    continue; // Skip sponsored results for clean data
                }

                // Inner selectors relative to each 'card'
                const departureTimeElement = card.querySelector('div.VY2U .vmXl.vmXl-mod-variant-large span:nth-child(1)');
                const arrivalTimeElement = card.querySelector('div.VY2U .vmXl.vmXl-mod-variant-large span:nth-child(3)');
                const durationElement = card.querySelector('div.xdW8 .vmXl.vmXl-mod-variant-default');
                const stopsElement = card.querySelector('div.JWEO .JWEO-stops-text');
                const airlineImgElement = card.querySelector('div.c5iUd-leg-carrier img');

                let price = 'N/A';
                // Kayak's price structure can be tricky. Sometimes the price is in a separate section.
                // We need to look for the price div relative to the parent card of the entire result.
                // It appears 'Oihj' is the parent for the price section.
                // Let's go up to the common ancestor 'nrc6' and then then look for the price.
                const priceSection = card.querySelector('.nrc6-price-section .e2GB-price-text');
                if (priceSection) {
                    price = priceSection.innerText.trim();
                }

                data.push({
                    departureTime: departureTimeElement ? departureTimeElement.innerText.trim() : 'N/A',
                    arrivalTime: arrivalTimeElement ? arrivalTimeElement.innerText.trim() : 'N/A',
                    duration: durationElement ? durationElement.innerText.trim() : 'N/A',
                    stops: stopsElement ? stopsElement.innerText.trim() : 'N/A',
                    airline: airlineImgElement ? airlineImgElement.alt.trim() : 'N/A',
                    price: price
                });
            }
            return data;
        });

        console.log('Extracted Flight Data:', JSON.stringify(extractedFlightData, null, 2));

        // Optional: Take screenshot after data extraction
        await playwright.takeScreenshot('kayak_flight_results_extracted_data.png');
        console.log('Screenshot saved to kayak_flight_results_extracted_data.png');

    } catch (error) {
        console.error('Test failed:', error);
    } finally {
        await playwright.close();
    }
}

testFlightSearch();