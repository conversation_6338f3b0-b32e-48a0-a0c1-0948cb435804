{"agency": {"name": "Smart Vacation Planner", "description": "An agency that creates personalized vacation plans"}, "agents": {"goalPlanner": {"id": "goalPlanner", "name": "Goal Planning Agent", "description": "Converts high-level vacation ideas into structured plans", "role": "You are a data processing agent. Your ONLY job is to take the user's input and convert it into a structured JSON object. You MUST NOT add, change, or interpret the data in any way. Your output MUST be a single JSON object containing all the original input fields: vacationIdea, preferences, travelDates, budget, weatherPreferences, culturalInterests, lodgingPreferences, interests, and budgetLevel. Do NOT add any other fields or creative content.", "goals": ["Understand user's vacation intent.", "Parse all provided initial preferences into a structured JSON output.", "Provide a comprehensive, structured JSON output for subsequent agents."], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.7, "maxOutputTokens": 2048}}, "destinationResearcher": {"id": "destination<PERSON><PERSON><PERSON><PERSON>", "name": "Destination Research Agent", "description": "Researches a specific destination based on preferences", "role": "You are a destination expert. You will receive an input object named 'parsedVacationDetails' which contains all vacation preferences, including the destination. Your task is to research the provided destination based on these preferences. IMPORTANT: You MUST use the search tool to get up-to-date information about the destination. For each of the following categories, use the search tool to get specific information: weather, accommodations, activities, and cultural sites. For example, to research weather, use [TOOL: travelSearch({\"query\": \"weather in [destination]\"})]. To research accommodations, use [TOOL: travelSearch({\"query\": \"accommodations in [destination]\"})]. Similarly for activities and cultural sites. Your output MUST be a single, valid JSON object containing a detailed analysis of the provided destination, with top-level keys for 'weather', 'accommodations', 'activities', and 'culturalSites'.", "goals": ["Research the provided destination using the search tool", "<PERSON>ather comprehensive information about the destination based on user preferences", "Provide a detailed analysis of the destination"], "provider": "gemini", "llmConfig": {"model": "gemini-2.5-flash-lite", "temperature": 0.7, "maxOutputTokens": 3072}, "tools": {"travelSearch": "travelSearch"}}}, "team": {"vacationTeam": {"name": "Vacation Planning Team", "description": "A team that creates comprehensive vacation plans", "agents": ["goalPlanner", "destination<PERSON><PERSON><PERSON><PERSON>"], "jobs": {"planGoals": {"description": "Convert vacation idea into structured plan", "agent": "goalPlanner", "inputs": {"vacationIdea": "Relaxing beach vacation in Miami with some cultural experiences", "preferences": "Beachfront accommodations, local cuisine, some water activities, cultural sites", "travelDates": "Aug 15-22, 2025", "budget": "Mid-range ($3000-5000)", "weatherPreferences": "Warm and sunny", "culturalInterests": "Local history, cuisine, architecture", "lodgingPreferences": "Beachfront resort with pool", "interests": "Snorkeling, local markets, historical sites, beach relaxation", "budgetLevel": "Mid-range with some splurges", "departureLocation": "Riverdale, GA"}}, "researchDestinations": {"description": "Research and compare potential destinations", "agent": "destination<PERSON><PERSON><PERSON><PERSON>", "inputs": {"parsedVacationDetails": "{{planGoals.output}}"}}}, "workflow": ["planGoals", "researchDestinations"]}}, "brief": {"beach-vacation-001": {"title": "Relaxing Beach Vacation in Miami, FL", "overview": "Plan a 7-day relaxing beach vacation in Miami, FL for a couple", "background": "<PERSON><PERSON><PERSON> in their 30s looking for a mix of relaxation and cultural experiences", "objective": "Create a complete 7-day itinerary with accommodations, dining, and activities", "vacationIdea": "Relaxing beach vacation in Miami with some cultural experiences", "preferences": "Beachfront accommodations, local cuisine, some water activities, cultural sites", "travelDates": "Aug 15-22, 2025", "budget": "Mid-range ($3000-5000)", "weatherPreferences": "Warm and sunny", "culturalInterests": "Local history, cuisine, architecture", "lodgingPreferences": "Beachfront resort with pool", "interests": "Snorkeling, local markets, historical sites, beach relaxation", "budgetLevel": "Mid-range with some splurges", "topic": "Beach Vacation"}}, "assignments": {"beach-vacation-001": {"assignedTo": "vacationTeam", "type": "team"}}}