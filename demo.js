// demo.js
import { Agent, <PERSON>MProvider } from './Agent.js'; // Import Agent and LLMProvider

// Mock LLM Provider (for demonstration)
class MockLLMProvider extends LLMProvider {
  async generateContent(options, tools) {
    console.log("MockLLM: Generating content with options:", options);
    console.log("MockLLM: Available tools:", Object.keys(tools));

    const lastMessage = options.contents[options.contents.length - 1].parts[0].text;

    if (lastMessage.includes("stock price of AAPL")) {
      // Simulate LLM deciding to use the getStockPrice tool
      return {
        candidates: [{
          content: {
            parts: [{
              text: 'The current stock price for AAPL is [TOOL: getStockPrice({"symbol": "AAPL"})]'
            }]
          }
        }]
      };
    } else if (lastMessage.includes("The current price of AAPL is $170")) {
      return {
        candidates: [{
          content: {
            parts: [{
              text: 'AAPL is currently at $170. This is above the threshold of $160.'
            }]
          }
        }]
      };
    } else if (lastMessage.includes("The current price of AAPL is $150")) {
      return {
        candidates: [{
          content: {
            parts: [{
              text: 'ALERT: AAPL has dropped to $150! This is below your threshold of $160. Consider taking action.'
            }]
          }
        }]
      };
    }

    return {
      candidates: [{
        content: {
          parts: [{
            text: 'I am not sure how to respond to that.'
          }]
        }
      }]
    };
  }
}

// Mock Tool to get stock price
const getStockPriceTool = {
  name: "getStockPrice",
  description: "Gets the current stock price for a given stock symbol.",
  schema: {
    name: "getStockPrice",
    description: "Gets the current stock price for a given stock symbol.",
    parameters: {
      type: "object",
      properties: {
        symbol: {
          type: "string",
          description: "The stock symbol (e.g., AAPL, GOOG)"
        }
      },
      required: ["symbol"]
    }
  },
  execute: async ({ symbol }) => {
    console.log(`Tool: Fetching price for ${symbol}`);
    // Simulate fetching real-time data
    if (symbol === "AAPL") {
      // Return different prices to simulate a drop
      const prices = [170, 165, 160, 155, 150];
      const randomIndex = Math.floor(Math.random() * prices.length);
      const price = prices[randomIndex];
      return { symbol, price };
    }
    return { symbol, price: null, error: "Symbol not found" };
  }
};

// Main execution
async function runIndependentStockMonitor() {
  console.log("Setting up independent stock monitoring agent...");

  const stockMonitorAgent = new Agent({
    id: "stockMonitorAgent",
    name: "Stock Monitor",
    description: "Monitors a stock price and alerts if it drops below a threshold.",
    role: "You are an assistant that monitors stock prices. When asked to monitor a stock, use the 'getStockPrice' tool. If the price of AAPL drops below $160, issue a critical alert.",
    llmProvider: new MockLLMProvider(),
    memoryConfig: { maxHistoryLength: 5 }, // Keep a short history
  });

  // Add the stock price tool to the agent
  stockMonitorAgent.addTool(getStockPriceTool);

  // Subscribe to agent events for logging/monitoring
  stockMonitorAgent.on('statusChanged', ({ newStatus }) => {
    console.log(`Agent Status: ${newStatus}`);
  });
  stockMonitorAgent.on('toolCalled', ({ toolName, params }) => {
    console.log(`Agent called tool: ${toolName} with params:`, params);
  });
  stockMonitorAgent.on('runCompleted', ({ response }) => {
    console.log(`Agent Run Completed. Final Response: "${response}"`);
  });
  stockMonitorAgent.on('runError', ({ error }) => {
    console.error(`Agent encountered an error during run: ${error}`);
  });

  const monitorInterval = 5000; // Check every 5 seconds
  const stockToMonitor = "AAPL";
  const alertThreshold = 160;

  console.log(`Starting independent monitoring of ${stockToMonitor} every ${monitorInterval / 1000} seconds. Alert threshold: $${alertThreshold}`);

  setInterval(async () => {
    console.log(`\n--- Checking ${stockToMonitor} price ---`);
    try {
      const response = await stockMonitorAgent.run(`What is the current stock price of ${stockToMonitor}?`);
    } catch (error) {
      console.error(`Error in monitoring interval: ${error.message}`);
    }
  }, monitorInterval);
}

runIndependentStockMonitor();