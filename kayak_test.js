import { kayakFlightSearch } from './kayakFlightSearchTool.js';
import { PlaywrightTool } from './playwrightTool.js'; // Adjust the path as needed

async function runKayakSearchAndDeleteDefaultOrigin() {
    const playwrightTool = new PlaywrightTool(); // Create an instance of the tool

    try {
        await playwrightTool.initialize({ headless: false }); // Run in headful mode to observe actions if needed for debugging

        // 1. Navigate to Kayak flights
        await playwrightTool.navigateTo('https://www.kayak.com/flights');
        await playwrightTool.page.waitForTimeout(2000); // Give some time for initial load and default values

        // 2. Delete default origin if present (e.g., "New York")
        // This is a common selector for default "From" field. You might need to adjust it
        // based on the actual HTML if it changes. Often it's an input with a value.
        const defaultOriginInputSelector = 'input[placeholder="Where from?"]';
        
        // Focus and clear the input field
        console.log("Attempting to clear default origin input...");
        await playwrightTool.page.waitForSelector(defaultOriginInputSelector, { state: 'visible', timeout: 10000 });
        await playwrightTool.page.fill(defaultOriginInputSelector, ''); // Clear the existing value
        await playwrightTool.page.keyboard.press('Escape'); // Press Escape to close any dropdown/autocomplete that might appear
        await playwrightTool.page.waitForTimeout(500); // Short pause after clearing
        console.log("Default origin input cleared.");

        // 3. Perform the flight search with specified details
        const origin = "Riverdale, GA"; // Or "Hartsfield-Jackson Atlanta International Airport" (ATL)
        const destination = "Miami, FL";
        const departureDate = "August 15, 2025";
        const returnDate = "August 22, 2025";

        console.log(`Starting flight search from ${origin} to ${destination} for ${departureDate} to ${returnDate}`);

        const results = await kayakFlightSearch(origin, destination, departureDate, returnDate);

        console.log("Flight search completed. Results found:", results.length);
        console.log(results);

        // After the search results load, take a screenshot of the entire page.
        // The kayakFlightSearch function already waits for '.result-inner'
        // We'll add another screenshot specifically for the full results page.
        await playwrightTool.page.waitForLoadState('networkidle'); // Wait for network to be idle
        await playwrightTool.page.waitForTimeout(5000); // Additional wait to ensure all dynamic content loads

        const screenshotPath = 'kayak_flight_prices_riverdale_miami.png';
        await playwrightTool.page.screenshot({ path: screenshotPath, fullPage: true });
        console.log(`Screenshot of entire results page saved to ${screenshotPath}`);

    } catch (error) {
        console.error("An error occurred during the flight search and screenshot:", error);
        // Take a screenshot on error as well for debugging
        await playwrightTool.takeScreenshot('error_during_kayak_script.png');
    } finally {
        await playwrightTool.close();
    }
}

// Execute the script
runKayakSearchAndDeleteDefaultOrigin();